import ContentHandler from "@/components/sections/ContentHandler";
import { TabItemProps } from "@/components/sections/sanityTypes";
import ScrollAnim from "@/components/ui/scrollAnim";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import React from "react";

const TabsSection: React.FC<{ tabs: TabItemProps[] }> = ({ tabs }) => {
    return (	
        <Tabs defaultValue={tabs[0].title} className="mt-5 flex flex-col items-center justify-center">
            <ScrollAnim className="max-w-full w-fit">
                <TabsList className="container max-w-full w-fit overflow-x-auto items-center justify-start mb-5">
                    {tabs.map((tab, i) => <TabsTrigger value={tab.title} key={i}>
                        {tab.title}
                    </TabsTrigger>)}
                </TabsList>
            </ScrollAnim>

            {tabs.map((tab, i) => <TabsContent value={tab.title} key={i} className="max-w-full" >
                <ContentHandler {...tab.content} />
            </TabsContent>)}
        </Tabs>
    )
}

export default TabsSection;