import Footer from "@/components/layout/footer";
import Header from "@/components/layout/header";
import { graphql, useStaticQuery } from "gatsby";
import React from "react";

export type Nav = {
	name: string;
	slug: { current: string };
	multiColumn: boolean;
	children: Nav[];
};

export type Socials = {
	name: string;
	url: string;
	icon: { svg: string };
	iconColor: { hex: string };
};

export type FooterLinks = {
	title: string;
	links: {
		name: string;
		slug: { current: string };
	}[];
};

type queryResult = {
	sanityNav: {
		links: Nav[];
		socials: Socials[];
		footerLinks: FooterLinks[];
	};
};

export default function Layout({ children }: { children: React.ReactNode }) {
	const navQuery: queryResult = useStaticQuery(graphql`
		query GetNav {
			sanityNav {
				links {
					name
					slug {
						current
					}
					multiColumn
					children {
						name
						slug {
							current
						}
						multiColumn
						children {
							name
							slug {
								current
							}
							multiColumn
						}
					}
				}
				socials {
					name
					url
					icon {
						svg
					}
					iconColor {
						hex
					}
				}
				footerLinks {
					title
					links {
						name
						slug {
							current
						}
					}
				}
			}
		}
	`);
	const navigation = navQuery.sanityNav.links;
	const socials = navQuery.sanityNav.socials;
	const footer = navQuery.sanityNav.footerLinks;

	return (
		<>
			<Header navigation={navigation} />
			<main>{children}</main>
			<Footer navigation={footer} socials={socials} />
		</>
	);
}
