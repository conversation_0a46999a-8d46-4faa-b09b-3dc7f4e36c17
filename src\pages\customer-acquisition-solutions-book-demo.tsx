import { SEO } from "@/components/layout/seo";
import { pageQueryResult } from "@/components/sections/sanityTypes";
import Section from "@/components/sections/section";
import { SectionDivider, topCurvePath } from "@/components/sections/sectionDividers";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Typography } from "@/components/ui/typography";
import { scrollDown, splitStringInRatio } from "@/lib/utils";
import { graphql, type HeadFC, type PageProps } from "gatsby";
import { ChevronRight } from "lucide-react";
// import React from "react";
import ScrollAnimation from "react-animate-on-scroll";

import React, { useState } from "react";

import { Loader2 } from "lucide-react";

import axios from "axios";

import { Dialog, DialogClose, DialogContent, Di<PERSON>D<PERSON><PERSON>, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";

const IndexPage: React.FC<PageProps<pageQueryResult>> = ({ data }) => {

	const [loading, setLoading] = useState(false);
	const [openDialog, setOpenDialog] = useState(false);

	const page = data.allSanityPage.nodes[0];
	const { part1, part2 } = splitStringInRatio(page.hero.title);

	
const SubmitHandler=(event: React.FormEvent<HTMLFormElement>)=>{
		event.preventDefault();
		setLoading(true);
		 const form = event.currentTarget;
		const name = (form.elements.namedItem("name") as HTMLInputElement).value;
  		const Componyemail = (form.elements.namedItem("companyEmail") as HTMLInputElement).value;
  		const mobileNo = (form.elements.namedItem("mobile") as HTMLTextAreaElement).value;
  		const Organization = (form.elements.namedItem("companyName") as HTMLTextAreaElement).value;
  		const Purpose = (form.elements.namedItem("purpose") as HTMLTextAreaElement).value;

		const MailSubject= "[MBS] Landing Page - Received a submission from "+name+"";
		const FormName="LandingPage";
	const mobileRegex = /^(\+91|0)?[6-9]\d{9}$/;
	if (!mobileRegex.test(mobileNo)) {
  		alert("Please enter a valid 10-digit mobile number.");
  		setLoading(false);
  		return;
	}
	 	if (!name || !Componyemail || !Purpose||!mobileNo||!Componyemail) {
		alert("Please fill out all fields.");
			setLoading(false);
		return;
	}

	const payload = {
		FormName,
		MailSubject,
		name,
		Componyemail,
		mobileNo,
		Organization,
		Purpose
	};
	const apiUrl = `${process.env.GATSBY_FORM_ENDPOINT}/v1/MBSMailAPI/WebEmailRequestMasterMethod`;
		axios.post(
		 //"https://sahibnkmitradevapiapp1.banksekure.com/MBSWebsite/v1/MBSMailAPI/WebEmailRequestMasterMethod",
		 apiUrl,payload,
		{
			headers: {
				"Content-Type": "application/json",
			},
		})
		.then((response)=>{
			if(response.status===200)
			{
				window.location.href="/thank-you-page";
				// setTimeout(() => {
				// 		setLoading(false);
				// 		setOpenDialog(true);
				// 	}, 500);
					form.reset();
			}
			console.log(response);
	})
	.catch((error)=>{
		alert(error);
			console.log(error);
	});
};

	return (
		<div className="w-full">
			{/* Hero image */}
			<section className="hero landing-page-hero flex flex-col justify-center items-center overflow-hidden bg-foreground">
				{/* Video */}
				{/* <div className="absolute h-full w-full translate-x-1/4">
					<video autoPlay muted loop playsInline preload="metadata" className="h-full w-full object-cover object-left filter brightness-75">
						<source src={video} type="video/webm" />
					</video>
				</div> */}

				<div className="h-full items-center">
					<div className="flex flex-col lg:flex-row items-center px-5 md:px-10">
						<div className="lg:w-2/3 w-full text-muted drop-shadow-outline">
							<ScrollAnimation animateIn="animate-in fade-in slide-in-from-bottom" duration={0.5} animateOnce>
								<Typography component="h1" size="xl4" weight="bold" className="mb-2">
									{part1}
									<br />
									<span className="text-secondary">{part2}</span>
								</Typography>
							</ScrollAnimation>
							<ScrollAnimation animateIn="animate-in fade-in slide-in-from-bottom" duration={0.5} delay={250} animateOnce>
								<Typography size="subh1" weight="light" component="h2">
									{page.hero.subtitle}
								</Typography>
							</ScrollAnimation>

							{/* <div className="flex gap-4 my-5 md:my-10">
								<ScrollAnimation animateIn="animate-in fade-in slide-in-from-bottom" duration={0.5} delay={500} animateOnce>
									<Button onClick={scrollDown} className="hover:text-primary-foreground">
										Get Started
										<ChevronRight className="size-5" />
									</Button>
								</ScrollAnimation>
							</div> */}
						</div>

						{/* <div className="lg:flex hidden p-10 h-full max-w-1/3 justify-center items-center">
							<StaticImage src="../images/homepage/home-banner-one-2.webp" alt="Banner" className="m-auto w-full max-h-full max-w-full aspect-square rounded-full shadow-xl" />
						</div> */}

						<div className="lg:w-2/5 w-full">
							<Card>
								<CardHeader>
									<Typography size="xl" component="h2">
										
									</Typography>
								</CardHeader>
								{/* <form method="POST" action={process.env.GATSBY_FORM_ENDPOINT}> */}
								<form onSubmit={SubmitHandler}>
									<input type="hidden" name="type" value="Book Demo" />
									<CardContent className="space-y-4">
										<Input name="name" placeholder="Name*" />
										<div className="flex gap-4">
											<Input name="mobile" type="text" placeholder="Mobile Number*" />
											<Input type="email" name="companyEmail" placeholder="Email ID*" />
										</div>
										<Input name="companyName" placeholder="Organization*" />
										<Textarea name="purpose" placeholder="What are you looking for?" />
										<p>All your personal information will remain confidential and will be handled in accordance with our Data Protection and <a href="/privacy-policy" className="text-secondary">Privacy Policy</a></p>
									
									</CardContent>
									<CardFooter className="flex justify-end">
										{/* <Button type="submit" className="w-full">
											Submit
										</Button> */}
											<Button disabled={loading} type="submit" variant="secondary" size="sm" className="w-full">
																	{loading && <Loader2 className="animate-spin" />}
																		Submit
											</Button>
									</CardFooter>
								</form>
								<Dialog open={openDialog} onOpenChange={setOpenDialog}>

									<DialogContent>

										<DialogHeader>

											<DialogTitle>

												Thank you for reaching out!

											</DialogTitle>

											<DialogDescription>

												We've received your message and will get back to you as soon as possible. Have a great day!

											</DialogDescription>

										</DialogHeader>

										<DialogFooter>

											<DialogClose asChild>

												<Button size='sm'>

													Close

												</Button>

											</DialogClose>

										</DialogFooter>

									</DialogContent>

								</Dialog>

								{/* <form method="POST" action={process.env.GATSBY_FORM_ENDPOINT}>
									<input type="hidden" name="type" value="Book Demo" />
									<CardContent className="space-y-4">
										<Input name="name" placeholder="Name *" />
										<div className="flex gap-4">
											<Input name="mobile" placeholder="Mobile Number*" />
											<Input name="companyEmail" placeholder="Work Email ID*" />
										</div>
										<Input name="organization" placeholder="Organization*" />
										<Textarea name="purpose" placeholder="What are you looking for?" />
									</CardContent>
									<CardFooter className="flex justify-end">
										<Button type="submit" className="w-full">
											Submit
										</Button>
									</CardFooter>
								</form> */}
							</Card>
						</div>
					</div>
				</div>

				

				<div className={`w-full absolute bottom-0 -z-10 translate-y-1/2`}>
					<svg id="bottomWave" xmlns="http://www.w3.org/2000/svg" className={`w-full block text-background`} viewBox="0 0 1440 224" preserveAspectRatio="none">
						<path fill="currentColor" fillOpacity="1" d={topCurvePath} />
					</svg>
				</div>
			</section>

			{page.sections.map((section, i) => (
				<React.Fragment key={i}>
					<Section {...section} last={page.sections.length - 1 === i} />
					{page.sections.length - 1 !== i && section.bg !== "dark" && page.sections[i + 1].bg !== "dark" && <SectionDivider />}
				</React.Fragment>
			))}
		</div>
	);
};

export default IndexPage;

export const Head: HeadFC<pageQueryResult> = ({ data }) => <SEO seo={data.allSanityPage.nodes[0].seo} />;

export const query = graphql`
	query PageQuery {
		allSanityPage(filter: { slug: { current: { eq: "landing-page" } } }) {
			nodes {
				title
				seo {
					title
					description
					keywords
					canonicalUrl
					ogTitle
					ogDescription
					ogImage {
						asset {
							publicUrl
							url
						}
					}
					noindex
					structuredData
				}
				hero {
					title
					subtitle
					cta
					ctaIcon {
						svg
					}
					ctaLink
					bgImage {
						asset {
							gatsbyImageData
							url
							publicUrl
						}
						alt
						title
					}
					imageAlign
				}
				sections {
					title
					subtitle
					id
					bg
					statistics {
						titleString
						titleNumber
						subtitle
						type
						icon {
							svg
						}
						image {
							asset {
								gatsbyImageData
								url
								publicUrl
							}
							alt
							title
						}
					}
					content {
						type
						content {
							title
							content: _rawContent
							cta
							ctaIcon {
								svg
							}
							ctaLink
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							imageAlign
							imageAsBg
						}
						
						bigList {
							title
							description: _rawDescription
						}
						carousel {
							type
							delay
							contentType
							iconCards {
								type
								image {
									asset {
										gatsbyImageData
										url
										publicUrl
									}
									alt
									title
								}
								icon {
									svg
								}
								title
								description: _rawDescription
							}
							carousel {
								title
								content: _rawContent
								cta
								ctaIcon {
									svg
								}
								ctaLink
								image {
									asset {
										gatsbyImageData
										url
										publicUrl
									}
									alt
									title
								}
								imageAlign
								imageAsBg
							}
						}
						iconCards {
							title
							type
							icon {
								svg
							}
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							description: _rawDescription
						}
						icons {
							title
							type
							icon {
								svg
							}
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
						}
						offices {
							name
							description
							address
							phones
							fax
							email
							location {
								lat
								lng
							}
						}
						employees {
							name
							position
							profile: _rawProfile
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
						}
						product {
							title
							cta
							ctaLink
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							highlights: _rawHighlights
							description: _rawDescription
						}
						tabs {
							title
							content {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								columns {
									splitRatio
									sticky
									contentLeft {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
									contentRight {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
						}
						columns {
							splitRatio
							sticky
							contentLeft {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								tabs {
									title
									content {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
							contentRight {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								tabs {
									title
									content {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
`;
