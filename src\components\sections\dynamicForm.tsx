import { DynamicFormProps, FormField } from "@/components/sections/sanityTypes";
import { useSection } from "@/components/sections/section";
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, <PERSON>Footer, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import ScrollAnim from "@/components/ui/scrollAnim";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Typography } from "@/components/ui/typography";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import React, { useState } from "react";
import axios from "axios";

interface FormFieldProps extends FormField {
	className: string;
}

const FormFieldComponent: React.FC<FormFieldProps> = ({ name, label, type, options, required, placeholder, className }) => {
	required = required ?? false;

	switch (type) {
		case 'number':
		case 'password':
		case 'email':
			return <Input name={name} type={type} required={required} placeholder={placeholder} className={className} />;
		case 'textarea':
			return <Textarea name={name} required={required} placeholder={placeholder} className={className} />;
		case 'checkbox':
			return <div className="flex items-center space-x-2 w-full">
				<Checkbox id={name} name={name} required={required} />
				<label htmlFor={name} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">{label}</label>
			</div>
		case 'radio':
			return <div className={className}>
				<label className="font-medium leading-none">{label}</label>
				<RadioGroup name={name} required={required}>
					{options?.map((option, i) => <div key={i} className="flex items-center space-x-2">
						<RadioGroupItem value={option} id={`${name}-${i}`} />
						<label htmlFor={`${name}-${i}`}>{option}</label>
					</div>)}
				</RadioGroup>
			</div>
		case 'select':
			return <Select name={name} required={required}>
				<SelectTrigger className={className}>
					<SelectValue placeholder={label} />
				</SelectTrigger>
				<SelectContent>
					{options?.map((option, i) => <SelectItem key={i} value={option}>
						{option}
					</SelectItem>)}
				</SelectContent>
			</Select>
		case 'file':
			return <div className={cn("space-y-2", className)}>
				<label htmlFor={name} className="text-sm font-medium leading-none">{label}</label>
				<Input name={name} type="file" required={required} accept=".pdf" id={name} />
			</div>
		default:
			return <Input name={name} required={required} placeholder={placeholder} className={className} />;
	}
}

const DynamicForm: React.FC<DynamicFormProps> = ({ title, formFields }) => {
	const { hasTitle } = useSection();
	const [loading, setLoading] = useState(false);
	const [openDialog, setOpenDialog] = useState(false);

	const onSubmit = async  (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();
		setLoading(true);
		const formData = new FormData(e.currentTarget);
		const data = Object.fromEntries(formData.entries());
 		const form = e.currentTarget;
		
		const Name =(form.elements.namedItem("Name") as HTMLTextAreaElement).value;
		const Email =(form.elements.namedItem("Email") as HTMLTextAreaElement).value;
		const Phone =(form.elements.namedItem("Phone") as HTMLTextAreaElement).value;

		const CVInput = form.elements.namedItem("cv") as HTMLInputElement;
		const CVFile = CVInput?.files?.[0];

		const mobileRegex = /^(\+91|0)?[6-9]\d{9}$/;
		if (!mobileRegex.test(Phone)) {
  		alert("Please enter a valid 10-digit mobile number.");
  		setLoading(false);
  		return;
		}
   	const cvFile = form.elements.namedItem("cv") as HTMLInputElement;
    if (!cvFile?.files || cvFile.files.length === 0) {
      alert("Please upload your CV.");
      setLoading(false);
      return;
    }
	
 	if (!CVFile) {
    alert("Please upload your CV.");
    setLoading(false);
    return;
  	}
	const data1 = new FormData();
    data1.append('Name', Name);
    data1.append('Email', Email);
    data1.append('Phone',Phone);
    data1.append('CV',CVFile); 
		const apiUrl = `${process.env.GATSBY_FORM_ENDPOINT}/v1/MBSMailAPI/MBSCareersMailAPI`;
	const response =await axios.post(
		//"https://sahibnkmitradevapiapp1.banksekure.com/MBSWebsite/v1/MBSMailAPI/MBSCareersMailAPI"
		apiUrl,data1)
		.then((response)=>{
			if(response.status===200)
			{
				window.location.href="/thank-you-page";
					// setTimeout(() => {
					// 	setLoading(false);
					// 	setOpenDialog(true);
					// }, 500);
					form.reset();
			}
			console.log(response);
	})
	.catch((error)=>{
		alert(error);
			console.log(error);
	});


		console.log(data);

		// setTimeout(() => {
		// 	setLoading(false);
		// 	setOpenDialog(true);
		// }, 2000);
	}

	const isTextField = (field: FormField) => ['text', 'number', 'email', 'password'].includes(field.type);

	const groupInputs = () => {
		const groups: FormField[][] = [];
		for (let i = 0; i < formFields.length; i++) {
			if (
				i != formFields.length - 1 && // Not the last field
				isTextField(formFields[i]) &&
				isTextField(formFields[i + 1])
			) {
				//push pair
				const pair = [formFields[i], formFields[i + 1]]
				groups.push(pair);
				i++; // Skip the next field as it's already handled
			} else {
				// Default to w-full for all other cases
				groups.push([formFields[i]]);
			}
		}
		return groups;
	}

	const groups = groupInputs();

	return (
		<ScrollAnim className="h-full">
			<Card className="h-full">
				<form onSubmit={onSubmit} className="h-full flex flex-col" encType="multipart/form-data">
					<CardHeader>
						<CardTitle>
							<Typography component={hasTitle ? 'h3' : 'h2'} size='xl'>
								{title}
							</Typography>
						</CardTitle>
					</CardHeader>
					<CardContent className="flex flex-col gap-4 grow">
						{groups.map((g, i) => <div key={i} className="flex gap-4">
							{g.map((field, j) => <FormFieldComponent key={j} {...field} className='w-full' />)}
						</div>)}
					</CardContent>
					<CardFooter>
						<Button disabled={loading} type="submit" variant="secondary" size="sm" className="w-full">
							{loading && <Loader2 className="animate-spin" />}
							Submit
						</Button>
					</CardFooter>
				</form>

				<Dialog open={openDialog} onOpenChange={setOpenDialog}>
					<DialogContent>
						<DialogHeader>
							<DialogTitle>
								Thank you for reaching out!
							</DialogTitle>
							<DialogDescription>
								We've received your message and will get back to you as soon as possible. Have a great day!
							</DialogDescription>
						</DialogHeader>
						<DialogFooter>
							<DialogClose asChild>
								<Button size='sm'>
									Close
								</Button>
							</DialogClose>
						</DialogFooter>
					</DialogContent>
				</Dialog>
			</Card>
		</ScrollAnim>
	)
}

export default DynamicForm;