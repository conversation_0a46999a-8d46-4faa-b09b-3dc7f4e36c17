import { IconProps } from "@/components/sections/sanityTypes";
import ScrollAnim from "@/components/ui/scrollAnim";
import { Typography } from "@/components/ui/typography";
import { GatsbyImage } from "gatsby-plugin-image";
import React from "react";
import SVG from "react-inlinesvg";

const Icons: React.FC<{ icons: IconProps[] }> = ({ icons }) => {
	return (
		<div className="flex flex-wrap justify-center gap-4 md:gap-8">
			{icons.map((icon, index) => {
				return (
					<ScrollAnim
						key={index}
						delay={(index % 4) * 100}
						className="flex basis-1/3 md:basis-1/5 justify-center"
					>
						<div className="flex flex-col items-center justify-center p-4 transition-transform transform hover:scale-105 text-center">
							{icon.type === "image"
								? icon.image?.asset?.gatsbyImageData && (
										<GatsbyImage
											image={
												icon.image.asset.gatsbyImageData
											}
											alt={
												icon.image?.alt ||
												icon.title ||
												"image"
											}
											title={
												icon.image?.title ||
												icon.title ||
												"image"
											}
											className="h-20 w-20"
											objectFit="contain"
										/>
								  )
								: icon.icon && (
										<SVG
											src={icon.icon.svg}
											title={icon.title}
											className="size-20 text-primary"
											style={{}}
										/>
								  )}
							<Typography weight="medium" className="mt-2">
								{icon.title}
							</Typography>
						</div>
					</ScrollAnim>
				);
			})}
		</div>
	);
};

export default Icons;
