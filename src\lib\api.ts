const UPLOADS_PLAYLIST_ID = "UUluDseiTAZiCS1VzCmTjUqA"
const YT_API_KEY = "AIzaSyBtge0oE9Yzk_J9sVm9IsQWu86fHlaZOGM"
const ENDPOINT = "https://www.googleapis.com/youtube/v3/playlistItems"

type videoResult = {
    nextPageToken: string | boolean;
    videos: {
        date: string,
        title: string,
        videoID: string,
    }[]
}

type query = {
    playlistId: string;
    key: string;
    part: string;
    maxResults: string;
    pageToken?: string;
}

export async function GetYoutubeVideos(pageToken: string = "") {
    let query: query = {
        playlistId: UPLOADS_PLAYLIST_ID,
        key: YT_API_KEY,
        part: 'snippet',
        maxResults: '4'
    }

    if (pageToken) query.pageToken = pageToken;

    const result = await fetch(`${ENDPOINT}?${new URLSearchParams(query)}`)

    const json = await result.json();
    
    const videos: videoResult = {
        nextPageToken: json?.nextPageToken || false,
        videos: json.items.map((item: any) => ({
            date: item.snippet.publishedAt,
            title: item.snippet.title,
            videoID: item.snippet.resourceId.videoId
        }))
    }

    return videos;
}