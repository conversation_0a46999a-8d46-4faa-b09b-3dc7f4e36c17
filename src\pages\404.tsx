import * as React from "react"
import { Link, HeadFC, PageProps } from "gatsby"

const NotFoundPage: React.FC<PageProps> = () => {
  return (
    <div className="h-screen flex justify-center items-center">
        <div className="container mx-auto">
            <p className="text-9xl font-black text-center">
                404
            </p>
            <p className="text-2xl font-semibold text-center">
                Page not found
            </p>
        </div>
    </div>
  )
}

export default NotFoundPage

export const Head: HeadFC = () => <title>Not found</title>
