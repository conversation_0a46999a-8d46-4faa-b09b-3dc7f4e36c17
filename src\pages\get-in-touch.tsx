import { SEO } from "@/components/layout/seo";
import HeroSection from "@/components/sections/hero";
import { pageQueryResult } from "@/components/sections/sanityTypes";
import Section from "@/components/sections/section";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Typography } from "@/components/ui/typography";
import { graphql, HeadFC, PageProps } from "gatsby";
import { Mail, Phone } from "lucide-react";

import React, { useState } from "react";

import { Loader2 } from "lucide-react";

import axios from "axios";

import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";


export default function Page() {
	const [loading, setLoading] = useState(false);
	const [openDialog, setOpenDialog] = useState(false);
	const SubmitHandler = async (event: React.FormEvent<HTMLFormElement>)=>{
		event.preventDefault();
		setLoading(true);
		   
		const form = event.currentTarget;
		const name = (form.elements.namedItem("name") as HTMLInputElement).value;
  		const email = (form.elements.namedItem("email") as HTMLInputElement).value;
  		const message = (form.elements.namedItem("message") as HTMLTextAreaElement).value;
	    const MailSubject= "[MBS] Contact Us - Received a submission from  "+name+"";
		const FormName="ContactUs";
		
		const apiUrl = `${process.env.GATSBY_FORM_ENDPOINT}/v1/MBSMailAPI/WebEmailRequestMasterMethod`;
		//alert(apiUrl);
	 	if (!name || !email || !message) {
		alert("Please fill out all fields.");
				setLoading(false);
		return;
		}

	const payload = {
		FormName,
		MailSubject,
		name,
		email,
		message,
	};

	//const response = await axios.post("https://sahibnkmitradevapiapp1.banksekure.com/MBSWebsite/v1/MBSMailAPI/WebEmailRequestMasterMethod"
	const response = await axios.post(apiUrl,payload,
		{
			headers: {
				"Content-Type": "application/json",
			},
		})
		.then((response)=>{
			if(response.status===200)
			{
					window.location.href="/thank-you-page";
					// 	setTimeout(() => {
					// 	setLoading(false);
					// 	setOpenDialog(true);
					// }, 500);
					form.reset();
				//alert("Thank you for contacting us! We'll get back to you shortly.");
			}
			console.log(response);
	})
	.catch((error)=>{
		alert(error);
			console.log(error);
	});
};


// const Page: React.FC<PageProps<pageQueryResult>> = ({ data }) => {
// 	const hero = data.allSanityPage.nodes[0].hero;
	return (
		<div className="w-full">
			{/* <HeroSection {...hero} /> */}

			<Section id="get-in-touch" title="" subtitle="" statistics={[]} last>
				<div className="flex flex-col lg:flex-row gap-8">
					<Card className="lg:w-1/2">
						{/* <form method="POST" action={process.env.GATSBY_FORM_ENDPOINT}> */}
						<form onSubmit={SubmitHandler}>
							<input type="hidden" name="type" value="Get in touch" />
							<CardHeader>
								<CardTitle>
									<Typography component="h3" size="xxl">
										Hi, We'd love to hear from you.
									</Typography>
								</CardTitle>
							</CardHeader>
							<CardContent className="flex flex-col gap-4">
								<Input placeholder="Name*" name="name" />
								<Input placeholder="Email*" name="email" type="email" />
								<Textarea placeholder="Message*" name="message" />
								<p>All your personal information will remain confidential and handled as per our Data Protection and <a href="/privacy-policy" className="text-secondary">Privacy Policy</a></p>
							</CardContent>
							<CardFooter>
								{/* <Button type="submit" variant="secondary" size="sm" className="w-full">
									Send Enquiry
								</Button> */}
								<Button disabled={loading} type="submit" variant="secondary" size="sm" className="w-full">
							{loading && <Loader2 className="animate-spin" />}
								Send Enquiry
								</Button>
							</CardFooter>
						</form>

						<Dialog open={openDialog} onOpenChange={setOpenDialog}>

								<DialogContent>

									<DialogHeader>

										<DialogTitle>

											Thank you for reaching out!

										</DialogTitle>

										<DialogDescription>

											We've received your message and will get back to you as soon as possible. Have a great day!

										</DialogDescription>

									</DialogHeader>

									<DialogFooter>

										<DialogClose asChild>

											<Button size='sm'>

												Close

											</Button>

										</DialogClose>

									</DialogFooter>

								</DialogContent>

							</Dialog>


					</Card>
					<Card className="lg:w-1/2 p-6">
						<div>
							<Typography component="h4" size="xl">
								Registered Office
							</Typography>
							<Typography className="mt-2">Manipal Business Solutions Private Limited, Udayavani Building, Press Corner, Manipal – 576104, Karnataka</Typography>
							{/* <Typography className="mt-2 text-secondary">
								<a href="tel:+91 820 2205000" className="flex gap-2">
									<Phone /> +91 820 2205000
								</a>
							</Typography> */}
							<Typography className="mt-2 text-secondary break-all">
								<a href="mailto:<EMAIL>" className="flex gap-2">
									<Mail />
									<EMAIL>
								</a>
							</Typography>
						</div>
						<Separator className="my-4" />
						<div>
							<Typography component="h4" size="xl">
								Corporate Office
							</Typography>
							<Typography className="mt-2">
								Manipal Business Solutions Private Limited, International Home Deco Park (“IHDP”), Plot No. 7, 5th Floor, Sector 127, Taj Express Way, Noida, Uttar Pradesh - 201 301
							</Typography>
							{/* <Typography className="mt-2 text-secondary">
								<a href="tel:+91 124 4020030" className="flex gap-2">
									<Phone />
									+91 124 4020030
								</a>
							</Typography> */}
							<Typography className="mt-2 text-secondary">
								<a href="tel:0120-6483800" className="flex gap-2">
									<Phone />
									0120-6483800
								</a>
							</Typography>
							<Typography className="mt-2 text-secondary break-all">
								<a href="mailto:<EMAIL>" className="flex gap-2">
									<Mail />
									<EMAIL>
								</a>
							</Typography>
						</div>
					</Card>
				</div>
			</Section>
		</div>
	);
};

//export default Page;
export const Head: HeadFC<pageQueryResult> = ({ data }) => <SEO seo={data.allSanityPage.nodes[0].seo} />;

export const query = graphql`
	query PageQuery {
		allSanityPage(filter: { slug: { current: { eq: "get-in-touch" } } }) {
			nodes {
				title
				seo {
					title
					description
					keywords
					canonicalUrl
					ogTitle
					ogDescription
					ogImage {
						asset {
							publicUrl
							url
						}
					}
					noindex
					structuredData
				}
				hero {
					title
					subtitle
					cta
					ctaIcon {
						svg
					}
					ctaLink
					bgImage {
						asset {
							gatsbyImageData
							url
							publicUrl
						}
						alt
						title
					}
					imageAlign
				}
				sections {
					title
					subtitle
					id
					bg
					statistics {
						titleString
						titleNumber
						subtitle
						type
						icon {
							svg
						}
						image {
							asset {
								gatsbyImageData
								url
								publicUrl
							}
							alt
							title
						}
					}
					content {
						type
						content {
							title
							content: _rawContent
							cta
							ctaIcon {
								svg
							}
							ctaLink
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							imageAlign
							imageAsBg
						}
						timeline {
							title
							date
							description: _rawDescription
						}
						bigList {
							title
							description: _rawDescription
						}
						carousel {
							type
							delay
							contentType
							iconCards {
								type
								image {
									asset {
										gatsbyImageData
										url
										publicUrl
									}
									alt
									title
								}
								icon {
									svg
								}
								title
								description: _rawDescription
							}
							carousel {
								title
								content: _rawContent
								cta
								ctaIcon {
									svg
								}
								ctaLink
								image {
									asset {
										gatsbyImageData
										url
										publicUrl
									}
									alt
									title
								}
								imageAlign
								imageAsBg
							}
						}
						iconCards {
							title
							type
							icon {
								svg
							}
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							description: _rawDescription
						}
						icons {
							title
							type
							icon {
								svg
							}
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
						}
						offices {
							name
							description
							address
							phones
							fax
							email
							location {
								lat
								lng
							}
						}
						employees {
							name
							position
							profile: _rawProfile
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
						}
						product {
							title
							cta
							ctaLink
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							highlights: _rawHighlights
							description: _rawDescription
						}
						tabs {
							title
							content {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								timeline {
									title
									date
									description: _rawDescription
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								columns {
									splitRatio
									sticky
									contentLeft {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
									contentRight {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
						}
						columns {
							splitRatio
							sticky
							contentLeft {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								timeline {
									title
									date
									description: _rawDescription
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								tabs {
									title
									content {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
							contentRight {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								timeline {
									title
									date
									description: _rawDescription
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								tabs {
									title
									content {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
`;
