export const googleGtagPluginConfig = {
  // You can add multiple tracking ids and a pageview event will be fired for all of them.
  trackingIds: [
    "G-8PYWJTLXMH", // Google Analytics / GA
  ],
  // This object gets passed directly to the gtag config command
  // This config will be shared across all trackingIds
  gtagConfig: {
    anonymize_ip: true,
    cookie_expires: 0,
  },
  // This object is used for configuration specific to this plugin
  pluginConfig: {
    // Puts tracking script in the head instead of the body
    head: false,
    // Setting this parameter is also optional
    respectDNT: true,
    // Defaults to https://www.googletagmanager.com
    origin: "https://www.googletagmanager.com",
  },
};