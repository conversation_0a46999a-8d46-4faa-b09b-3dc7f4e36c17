import { HeroSectionProps } from "@/components/sections/sanityTypes";
import { topCurvePath } from "@/components/sections/sectionDividers";
import { Button } from "@/components/ui/button";
import ScrollAnim from "@/components/ui/scrollAnim";
import { Typography } from "@/components/ui/typography";
import { scrollDown, splitStringInRatio } from "@/lib/utils";
import { Link } from "gatsby";
import { GatsbyImage } from "gatsby-plugin-image";
import { ChevronRight, ChevronsDown } from "lucide-react";
import React, { useEffect, useState } from "react";

const HeroSection: React.FC<HeroSectionProps> = ({ title, subtitle, cta, ctaLink, bgImage: image, imageAlign }) => {
	const [titleStart, setTitleStart] = useState<string>("");
	const [titleEnd, setTitleEnd] = useState<string>("");

	useEffect(() => {
		const { part1, part2 } = splitStringInRatio(title);
		setTitleStart(part1);
		setTitleEnd(part2);
	}, []);
	return (
		<section id="hero" className="flex flex-col items-center justify-center hero h-screen relative text-accent overflow-hidden">
			{/* Image & shade */}
			<div
				className="!absolute top-0 left-0 h-full w-full pt-20"
				// style={{ mask: 'url(#waveMask)' }}
			>
				{image?.asset ? (
					<GatsbyImage
						className="h-full md:w-full"
						image={image.asset.gatsbyImageData}
						objectPosition={imageAlign || "top"}
						alt={image?.alt || title || "Banner"}
						title={image?.title || title || "Banner"}
					/>
				) : null}

				<div className="absolute top-0 left-0 h-full w-full bg-slate-900/40" />
			</div>

			<div className="container absolute top-0 mx-auto h-full flex items-center justify-center py-20">
				<div className="text-center max-w-4xl space-y-5 p-8 md:p-10 drop-shadow-outline">
					<ScrollAnim>
						<Typography size="xl3" component="h1">
							{titleStart} <span className="text-secondary">{titleEnd}</span>
						</Typography>
					</ScrollAnim>

					<ScrollAnim delay={250}>
						<Typography size="subh1" className="text-base">
							{subtitle}
						</Typography>
					</ScrollAnim>
				</div>
			</div>

			<div className={`w-full max-h-40 absolute bottom-0 -z-10 translate-y-1/2`}>
				<svg id="bottomWave" xmlns="http://www.w3.org/2000/svg" className={`w-full block text-background`} viewBox="0 0 1440 224" preserveAspectRatio="none">
					<path fill="currentColor" fillOpacity="1" d={topCurvePath} />
				</svg>
			</div>

			{ctaLink && (
				<ScrollAnim delay={750} className="absolute bottom-28">
					<Link to={ctaLink}>
						<Button>
							{cta}
							<ChevronRight className="size-5" />
						</Button>
					</Link>
				</ScrollAnim>
			)}

			<a href="#" onClick={scrollDown} className="animate-scroll-bounce duration-750 absolute bottom-2 text-foreground">
				<ChevronsDown className="h-8 w-8" />
			</a>
		</section>
	);
};

export default HeroSection;
