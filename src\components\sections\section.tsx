import ContentHandler from "@/components/sections/ContentHandler";
import { SectionProps } from "@/components/sections/sanityTypes";
import { SectionLower, SectionUpper } from "@/components/sections/sectionDividers";
import ScrollAnim from "@/components/ui/scrollAnim";
import { Typography } from "@/components/ui/typography";
import { isContentEmpty, splitStringInRatio } from "@/lib/utils";
import { GatsbyImage } from "gatsby-plugin-image";
import React, { createContext, PropsWithChildren, useContext, useEffect, useState } from "react";
import { Props as AnimatedNumberProps } from "react-animated-numbers";
import SVG from "react-inlinesvg";

interface SectionPropsWithLast extends SectionProps {
	last?: boolean;
}

interface SectionContextType {
	bg: "default" | "dark";
	hasTitle: boolean;
}

const SectionContext = createContext<SectionContextType>({
	bg: "default",
	hasTitle: true,
});

export const useSection = () => useContext(SectionContext);

const Section: React.FC<PropsWithChildren<SectionPropsWithLast>> = ({ children, title, subtitle, content, id, bg = "default", statistics, last = false }) => {
	// Ensures that the animated numbers is only rendered client-side
	const [isClient, setIsClient] = useState(false);
	useEffect(() => {
		setIsClient(true);
	}, []);

	const AnimatedNumber = isClient ? require("react-animated-numbers").default : ({ animateToNumber }: AnimatedNumberProps) => <>{animateToNumber}</>;

	const { part1: titleStart, part2: titleEnd } = title
		? splitStringInRatio(title)
		: {
				part1: "",
				part2: "",
		  };

	const contentIsEmpty = isContentEmpty(content);

	return (
		<SectionContext.Provider value={{ bg, hasTitle: title ? true : false }}>
			<section id={id} className={`overflow-clip group/section first:pt-20 ${bg === "dark" && !last ? "drop-shadow-[0_0_45px_rgba(0,0,0,0.15)]" : ""}`}>
				<ScrollAnim noAnim={bg !== "dark"} delay={250}>
					{bg === "dark" && <SectionUpper />}
					<div className={`group-first/section:pt-10 ${bg == "dark" ? "bg-foreground text-muted " : last ? "pt-10" : "py-10"}`}>
						<div className={`${content?.type === "Carousel" ? "px-5 lg:px-0" : "container mx-auto px-5 md:px-1"}`}>
							{title && (
								<ScrollAnim>
									<Typography component="h2" size="xl3" className="text-center">
										{titleStart} <span className="text-secondary">{titleEnd}</span>
									</Typography>
								</ScrollAnim>
							)}
							{subtitle && (
								<ScrollAnim>
									<Typography size="lg" className="text-muted-foreground text-center mt-2">
										{subtitle}
									</Typography>
								</ScrollAnim>
							)}

							{content && (
								<div className={`${title ? "mt-10" : ""} ${bg === "dark" ? "" : ""}`}>
									<ContentHandler {...content} />
								</div>
							)}

							{children}

							{statistics?.length > 0 && (
								<div className={`grid grid-cols-${statistics.length / 2} lg:grid-cols-${statistics.length} gap-4 md:gap-8 ${contentIsEmpty ? "" : "mt-10"}`}>
									{statistics?.map((stat, index) => (
										<ScrollAnim key={index} delay={index * 150}>
											<div className={`flex flex-col items-center justify-center p-4 transition-transform transform hover:scale-105 text-center`}>
												<div className="p-4">
													{stat.type === "image" && stat.image?.asset?.gatsbyImageData && (
														<GatsbyImage
															image={stat.image.asset.gatsbyImageData}
															alt={stat.image?.alt || stat.subtitle || "image"}
															title={stat.image.title || stat.subtitle || "image"}
															className="h-20 w-20"
														/>
													)}
													{stat.type === "icon" && stat.icon && (
														<SVG
															src={stat.icon.svg}
															title={stat.subtitle}
															className={`size-20 ${stat.titleNumber ? "text-muted-foreground/75" : "text-primary"}`}
															style={{}}
														/>
													)}
												</div>
												<Typography size="xl3" weight="extrabold" className={`text-secondary flex`}>
													{stat.titleNumber && (
														<AnimatedNumber
															includeComma
															transitions={() => ({
																// type: "spring",
																duration: index + 1,
															})}
															animateToNumber={stat.titleNumber}
															locale="en-IN"
														/>
													)}
													{stat.titleString}
												</Typography>
												<Typography weight="bold" className="mt-1">
													{stat.subtitle}
												</Typography>
											</div>
										</ScrollAnim>
									))}
								</div>
							)}
						</div>
					</div>
					{(bg === "dark" || last) && <SectionLower bg={bg} last={last} />}
				</ScrollAnim>
			</section>
		</SectionContext.Provider>
	);
};

export default Section;
