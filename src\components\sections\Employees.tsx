import { Employee } from "@/components/sections/sanityTypes";
import { useSection } from "@/components/sections/section";
import { Avatar } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import ScrollAnim from "@/components/ui/scrollAnim";
import { Typography } from "@/components/ui/typography";
import sanitySerializers from "@/lib/sanity";
import { PortableText } from "@portabletext/react";
import { GatsbyImage } from "gatsby-plugin-image";
import * as React from "react";

const MemberCard: React.FC<Employee> = ({ name, position, profile: content, image }) => {
	const { hasTitle } = useSection();
	const profileTruncated = content[0].children[0].text.slice(0, 100) + "...";

	const alt = name || (content?.length > 0 && content[0].children[0].text) || "profile picture";

	return (
		<Dialog>
			<DialogTrigger asChild>
				<div className="w-full h-full sm:min-h-[300px] aspect-square sm:aspect-auto flex flex-col relative cursor-pointer group overflow-hidden rounded-lg">
					<GatsbyImage image={image.asset.gatsbyImageData} alt={alt} className="w-full h-full grayscale" objectPosition="top" />

					<div className="absolute bottom-0 right-0 w-full h-full bg-gradient-to-r from-secondary via-secondary/50 to-secondary translate-y-full group-hover:translate-y-0 transition-all duration-700 group-hover:delay-200 flex flex-col p-3 drop-shadow-outline text-background ease-in-out">
						<Typography component={hasTitle ? "h3" : "h2"} weight="bold" className="text-lg">
							{name}
						</Typography>
						<Typography weight="medium" className="text-[11px]">
							{position}
						</Typography>

						<Typography className="mt-auto h-0 hidden group-hover:block group-hover:h-auto transition-all duration-300 text-[11px]">
							{profileTruncated} <span className="text-foreground font-bold">Read More</span>
						</Typography>
					</div>

					<div className="absolute bottom-0 left-0 p-3 drop-shadow-outline text-background w-full flex flex-col h-min group-hover:translate-y-full transition-all duration-200 origin-bottom ease-in-out bg-gradient-to-r from-secondary via-secondary/50 to-secondary group-hover:bg-none group-hover:delay-0 delay-500">
						<Typography component={hasTitle ? "h3" : "h2"} weight="bold" className="text-lg">
							{name}
						</Typography>
						<Typography weight="medium" className="text-[11px]">
							{position}
						</Typography>
					</div>
				</div>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle className="flex gap-4 items-center">
						<Avatar>
							<GatsbyImage image={image.asset.gatsbyImageData} alt={alt} className="w-full h-full grayscale" objectPosition="top" />
						</Avatar>
						<Typography component={hasTitle ? "h3" : "h2"} size="xl">
							{name}
						</Typography>
					</DialogTitle>
				</DialogHeader>
				<DialogDescription className="max-h-[55vh] 2xl:max-h-[80-vh] overflow-auto scrollable -mr-2">
					<PortableText value={content} components={sanitySerializers(`mb-4 last:mb-0`)} />
				</DialogDescription>
				<DialogFooter>
					<DialogClose>
						<Button size="sm" variant="ghost">
							Close
						</Button>
					</DialogClose>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

const Employees: React.FC<{ employees: Employee[] }> = ({ employees }) => {
	// Ensures that the carousel autoplay is only rendered client-side
	// const [isClient, setIsClient] = React.useState(false);
	// React.useEffect(() => {
	// 	setIsClient(true);
	// }, []);
	// const Autoplay = isClient ? require('embla-carousel-autoplay') : null;

	const getGridClass = () => {
		const length = employees.length;

		const gridRowsMD: Record<number, string> = {
			1: "md:grid-rows-1",
			2: "md:grid-rows-2",
			3: "md:grid-rows-3",
			4: "md:grid-rows-4",
			5: "md:grid-rows-5",
			6: "md:grid-rows-6",
			7: "md:grid-rows-7",
			8: "md:grid-rows-8",
			9: "md:grid-rows-9",
			10: "md:grid-rows-10",
			11: "md:grid-rows-11",
			12: "md:grid-rows-12",
		};

		const gridRowsXL: Record<number, string> = {
			1: "xl:grid-rows-1",
			2: "xl:grid-rows-2",
			3: "xl:grid-rows-3",
			4: "xl:grid-rows-4",
			5: "xl:grid-rows-5",
			6: "xl:grid-rows-6",
			7: "xl:grid-rows-7",
			8: "xl:grid-rows-8",
			9: "xl:grid-rows-9",
			10: "xl:grid-rows-10",
			11: "xl:grid-rows-11",
			12: "xl:grid-rows-12",
		};

		// Calculate rows based on the length and number of columns at different breakpoints
		const getRowsClass = (md: number, xl?: number) => {
			const rowsMd = Math.ceil(length / md); // Calculate rows dynamically by dividing length by columns

			const rowsXl = xl ? Math.ceil(length / xl) : null;

			return rowsXl ? `${gridRowsMD[rowsMd]} ${gridRowsXL[rowsXl]}` : gridRowsMD[rowsMd]; // Return grid rows based on calculated rows
		};

		let gridColsClass = "";
		let gridRowsClass = "";

		// Logic for grid columns and rows based on length
		if (length % 6 === 0) {
			gridColsClass = "grid md:grid-cols-3 xl:grid-cols-6";
			gridRowsClass = getRowsClass(3, 6);
		} else if (length % 5 === 0) {
			gridColsClass = "grid md:grid-cols-5";
			gridRowsClass = getRowsClass(5);
		} else if (length % 4 === 0) {
			gridColsClass = "grid md:grid-cols-4";
			gridRowsClass = getRowsClass(4);
		} else if (length % 3 === 0) {
			gridColsClass = "grid md:grid-cols-3";
			gridRowsClass = getRowsClass(3);
		} else {
			gridColsClass = "grid md:grid-cols-4 xl:grid-cols-6";
			gridRowsClass = getRowsClass(4, 6);
		}

		// Return the combined grid columns and rows classes
		return `${gridColsClass} ${gridRowsClass}`;
	};

	const grid = getGridClass();

	return (
		<div className={`md:px-10 ${employees.length % 3 === 0 && employees.length & 1 ? "max-w-3xl" : "max-w-6xl"} mx-auto ${grid} grid-cols-1 gap-4 md:gap-8 items-stretch`}>
			{/* <Carousel plugins={isClient ? [Autoplay({ delay: 4000 })] : []}>
				<CarouselContent> */}
			{employees.map((employee, i) => (
				<ScrollAnim key={i}>
					<MemberCard {...employee} />
				</ScrollAnim>
			))}
			{/* </CarouselContent>

				<CarouselNext variant='ghost' className="hidden md:flex" />
				<CarouselPrevious variant='ghost' className="hidden md:flex" />
			</Carousel> */}
		</div>
	);
};

export default Employees;
