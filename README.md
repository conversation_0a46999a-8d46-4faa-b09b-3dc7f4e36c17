# Gatsby Project Setup and Deployment Guide

This guide explains how to run the Gatsby project locally with real-time updates from Sanity CMS, how to create production builds, zip them for deployment, and test your build locally.

## Prerequisites

-   Node.js (Recommended: v18.x or higher)
-   npm or yarn
-   Gatsby CLI (`npm install -g gatsby-cli` or `yarn global add gatsby-cli`)
-   Git (for version control)
-   Sanity CMS project configured and credentials (Sanity token for real-time updates)

---

## 🛠️ Installation

Install dependencies:

```bash
npm install
# or
yarn install
```

---

## 🚀 Running the Project Locally (with real-time updates from Sanity CMS)

To run your Gatsby project locally and automatically reflect changes published on Sanity CMS in real-time, follow these steps:

### Run Gatsby Development Server

```bash
npm dun develop
```

Your local development server will start at:

```
http://localhost:8000
```

Gatsby's GraphQL explorer will be available at:

```
http://localhost:8000/___graphql
```

Now, your Gatsby site will automatically reload whenever you publish or update content in your Sanity CMS.

---

## 📦 Building and Deploying

To create a production-ready build:

### Step 1: Verify environment variables in .env file:

```bash
GATSBY_FORM_ENDPOINT="abc"
```

Ensure that any variable required in build must have 'GATSBY\_' as a prefix.

### Step 2: Build the Project

```bash
npm run build
```

This generates a static version of your site into the `public` folder.

### Step 2: Zip the Build for Deployment

You can zip the `public` folder to easily upload it to your server:

```bash
zip -r build.zip public/
```

Now you can upload `public.zip` to your hosting server.

---

## 🧪 Testing the Production Build Locally

To preview the production build locally, use Gatsby's built-in server:

```bash
npm run serve
```

This will serve your built files locally at:

```
http://localhost:9000
```

---

## ✅ Summary of Commands

| Command                    | Description                             |
| -------------------------- | --------------------------------------- |
| `npm run develop`          | Start Gatsby local development server   |
| `nom run build`            | Build the Gatsby site (static files)    |
| `zip -r build.zip public/` | Zip the production build for deployment |
| `npm run serve`            | Preview production build locally        |

---

## Troubleshooting

-   If real-time updates aren't working, verify your Sanity token permissions and ensure your `.env` file is properly configured.
-   Ensure ports `8000` and `9000` are free before running the development or serve commands.

---

## 🔗 Helpful Resources

-   [Gatsby Documentation](https://www.gatsbyjs.com/docs/)
-   [Sanity CMS Integration Guide](https://www.sanity.io/docs/gatsby)

---
