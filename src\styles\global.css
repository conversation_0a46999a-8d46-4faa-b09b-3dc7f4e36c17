@tailwind base;
@tailwind components;
@tailwind utilities;
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Geist:wght@100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

html {
	scroll-behavior: smooth;
}

body {
	width: 100%;
	height: 100%;
	margin: 0;
	padding: 0;
}

a:focus-visible {
	outline: none;
}

button .button__blobs {
	height: 100%;
	filter: url(#goo);
	overflow: hidden;
	position: absolute;
	top: 0;
	left: 0;
	bottom: -3px;
	right: -1px;
	z-index: -1;
}

button .button__blobs div {
	width: 34%;
	height: 100%;
	border-radius: 100%;
	position: absolute;
	transition: transform 700ms ease, filter 650ms ease 400ms;
}

button .button__blobs div:nth-child(1) {
	left: -5%;
}

button .button__blobs div:nth-child(2) {
	left: 30%;
	transition-delay: 60ms, 400ms;
}

button .button__blobs div:nth-child(3) {
	left: 66%;
	transition-delay: 25ms, 400ms;
}

/* Debug ghost elements */
/* * {
    background: #000 !important;
    color: #0f0 !important;
    outline: solid #f00 1px !important;
} */

.list-decimal {
	list-style-type: decimal;
}

.text-outline {
	color: transparent;
	-webkit-text-stroke: 0.05rem hsl(var(--foreground));
}

.text-outline-accent {
	color: transparent;
	-webkit-text-stroke: 0.125rem hsl(var(--accent));
}

.text-outline-secondary {
	color: transparent;
	-webkit-text-stroke: 0.125rem hsl(var(--secondary));
}

.text-outline-primary {
	color: transparent;
	-webkit-text-stroke: 0.125rem hsl(var(--primary));
}

/* Target WebKit browsers (Chrome, Safari, Edge) */
.scrollable {
	overflow: auto;
	scrollbar-width: thin; /* for Firefox */
	scrollbar-color: auto transparent; /* thumb color + track color */
}

/* For Chrome, Safari and Edge */
.scrollable::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

.scrollable::-webkit-scrollbar-track {
	background: transparent;
}

.scrollable::-webkit-scrollbar-thumb {
	background-color: rgba(100, 100, 100, 0.6); /* Customize thumb color */
	border-radius: 4px;
}

@layer base {
	* {
		@apply border-border;
	}

	body {
		@apply bg-background text-foreground;
	}

	main {
		@apply isolate;
	}

	html {
		font-family: Poppins, sans-serif;
	}
}


/* 30-Jun-2025 */
.thankyou-logo{
	margin-top: -250px;
	margin-bottom: 30px;
}
.thankyou-logo svg{
	display: block;
	margin: 0 auto;
	width: 120px;
    height: auto;
	transform: none;
}
.thankyou-well{
	background: #ffffff;
    width: 90%;
    margin: 0 auto;
    max-width: 500px;
    padding: 30px;
    text-align: center;
    border-radius: 20px;
    --tw-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.1);
    --tw-shadow-colored: 0 0 12px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    margin-bottom: 100px;
    position: relative;
}
.thank-you-banner-bg svg{
	display: block;
	width: 100%;
	height: auto;
}
.thankyou-well .thankyou-icon{margin-bottom: 30px;}
.thankyou-well .thankyou-icon svg{
	display: block;
	margin: 0 auto;
}
.thankyou-well h5{
	margin-bottom: 30px;
	font-size: 2.25rem;
	line-height: 2.5rem;
	font-weight: 600;
	color: #2d90e1;
}
.thankyou-well h6{
	margin-bottom: 30px;
	font-size: 1.5rem;
	line-height: 2rem;
	font-weight: 600;
	color: #000000;
}
.thankyou-well p{
	font-size: 1rem;
    line-height: 1.5rem;
	color: #000000;
}
.thankyou-actions{
	margin-top: 30px;
	display: flex;
	justify-content: center;
}
.thankyou-actions .primary-button{
	background: #2d90e1;
	color: #ffffff;
	padding: 10px 20px;
	border-radius: 20px;
	text-decoration: none;
	font-size: 16px;;
	font-weight: 700;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: background-color 0.3s ease;
}
.thankyou-actions .primary-button svg{margin-left: 10px;}
.thankyou-actions .secondary-button{
	background: #ffffff;
	border: 2px solid #2d90e1;
	color: #2d90e1;
	padding: 10px 20px;
	border-radius: 20px;
	text-decoration: none;
	font-size: 16px;
	font-weight: 700;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: background-color 0.3s ease;
	margin-left: 10px;
}
.thankyou-actions .secondary-button svg{margin-left: 10px;}

@media only screen and (max-width : 640px) {
	.thankyou-actions{flex-direction: column;}
	.thankyou-actions .secondary-button{margin: 20px 0 0 0;}
	.thankyou-logo{margin: 100px 0 30px 0;}
	.thank-you-banner-bg{display: none;}
}
/* END 30-Jun-2025 */