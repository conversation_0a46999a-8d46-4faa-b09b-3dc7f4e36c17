// Create redirect from /banking-solutions/ to /banking/
    actions.createRedirect({
        fromPath: '/banking-solutions/',
        toPath: '/banking/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });

    // Also handle the version without trailing slash
    actions.createRedirect({
        fromPath: '/banking-solutions',
        toPath: '/banking/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });

    // Create redirect from /customer-acquisition-solutions/ to /cas/
    actions.createRedirect({
        fromPath: '/customer-acquisition-solutions/',
        toPath: '/cas/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });

    // Also handle the version without trailing slash
    actions.createRedirect({
        fromPath: '/customer-acquisition-solutions',
        toPath: '/cas/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });

     // Create redirect from /ckyc/ to /ckyc-solutions/
    actions.createRedirect({
        fromPath: '/ckyc-solutions/',
        toPath: '/ckyc/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });

    // Also handle the version without trailing slash
    actions.createRedirect({
        fromPath: '/ckyc-solutions',
        toPath: '/ckyc/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });

         // Create redirect from /contact-verification/ to /contact-point-verification/
    actions.createRedirect({
        fromPath: '/contact-point-verification/',
        toPath: '/contact-verification/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });

    // Also handle the version without trailing slash
    actions.createRedirect({
        fromPath: '/contact-point-verification',
        toPath: '/contact-verification/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });

      // Create redirect from /customer-onboarding/ to /customer-onboarding-solutions/
    actions.createRedirect({
        fromPath: '/customer-onboarding-solutions/',
        toPath: '/customer-onboarding/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });

    // Also handle the version without trailing slash
    actions.createRedirect({
        fromPath: '/customer-onboarding-solutions',
        toPath: '/customer-onboarding/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });


    
      // Create redirect from /hr-tech/ to /hr-tech-solutions/
    actions.createRedirect({
        fromPath: '/hr-tech-solutions/',
        toPath: '/hr-tech/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });

    // Also handle the version without trailing slash
    actions.createRedirect({
        fromPath: '/hr-tech-solutions',
        toPath: '/hr-tech/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });

          // Create redirect from /mas/ to /merchant-acquisition-solution/
    actions.createRedirect({
        fromPath: '/merchant-acquisition-solution/',
        toPath: '/mas/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });

    // Also handle the version without trailing slash
    actions.createRedirect({
        fromPath: '/merchant-acquisition-solution',
        toPath: '/mas/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });

          // Create redirect from /merchant-onboarding-solutions/ to /merchant-onboarding/
    actions.createRedirect({
        fromPath: '/merchant-onboarding-solutions/',
        toPath: '/merchant-onboarding/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });

    // Also handle the version without trailing slash
    actions.createRedirect({
        fromPath: '/merchant-onboarding-solutions',
        toPath: '/merchant-onboarding/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301
    });


    // Create redirect from /re-kyc-solutions/ to /re-kyc/
    actions.createRedirect({
        fromPath: '/re-kyc-solutions/',
        toPath: '/re-kyc/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301,
        force: true
    });

    // Also handle the version without trailing slash
    actions.createRedirect({
        fromPath: '/re-kyc-solutions',
        toPath: '/re-kyc/',
        isPermanent: true,
        redirectInBrowser: false,
        statusCode: 301,
        force: true
    });