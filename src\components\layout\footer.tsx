import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Input } from "@/components/ui/input";
import { Typography } from "@/components/ui/typography";
import Logo from "@/images/Logo.svg";
import { FooterLinks, Socials } from "@/layouts";
import { Link } from "gatsby";
import { ChevronDown } from "lucide-react";
import React from "react";
import SVG from "react-inlinesvg";

export default function Footer({ navigation, socials }: { navigation: FooterLinks[]; socials: Socials[] }) {
	const navSmall = navigation.filter((nav) => nav.links.length <= 6);
	const navLarge = navigation.filter((nav) => nav.links.length > 6);

	const year = new Date().getFullYear();

	const linkCols =
		navSmall.length === 5 ? "md:grid-cols-5" : navSmall.length === 4 ? "md:grid-cols-4" : navSmall.length === 3 ? "md:grid-cols-3" : navSmall.length === 2 ? "md:grid-cols-2" : "md:grid-cols-1";

	return (

		

		<footer className="bg-slate-100 w-full">
			<div className="mx-auto p-4 flex-col py-10">
				<div className="flex flex-col md:flex-row justify-between gap-5 md:gap-10 px-5 md:px-10">
					<div className={`container grid gap-5 md:gap-10 grid-cols-2 ${linkCols} md:grid-rows-1`}>
						{navSmall.map((nav, index) => (
							<div key={index} className="flex flex-col gap-4">
								<Typography weight="medium" className="text-primary">
									{nav.title}
								</Typography>
								<div className="flex flex-col gap-2">
									{nav.links.map((link, index) => (
										<Link key={index} to={`/${link.slug.current === "home" ? "" : link.slug.current}`} className="hover:text-secondary w-fit">
											<Typography weight="light">{link.name}</Typography>
										</Link>
									))}
								</div>
							</div>
						))}
					</div>

					<form className="flex flex-col gap-4 md:mx-auto" method="POST" action={process.env.GATSBY_FORM_ENDPOINT}>
						<Typography weight="medium" className="text-primary">
							Subscribe to our Newsletter
						</Typography>
						<div className="flex flex-col gap-4">
							<input type="hidden" name="type" value="Newsletter Signup" />
							<Input placeholder="Enter your email" name="email" className="text-xs md:text-sm bg-background" />
							<Button variant="secondary" size="sm" className="text-xs md:text-sm h-8 font-medium">
								Subscribe
							</Button>
						</div>
					</form>
				</div>
			</div>

			<Collapsible className="p-4">
				<div className="w-full hidden md:flex justify-center before:absolute before:border-t-2 before:border-foreground/20 before:top-1/2 before:left-1/2 before:right-1/2 before:-translate-x-1/2 before:h-[1px] before:w-11/12 relative">
					<CollapsibleTrigger asChild>
						<Button variant="outline" size="sm" className="bg-slate-100">
							More Links <ChevronDown className="size-5" />
						</Button>
					</CollapsibleTrigger>
				</div>
				<CollapsibleContent className="flex flex-wrap gap-4 lg:justify-evenly px-8 py-4">
					{navLarge.map((nav, index) => (
						<div key={index} className="flex flex-col gap-4">
							<Typography weight="medium" className="text-primary text-center w-full">
								{nav.title}
							</Typography>
							<div className="space-y-4 columns-2">
								{nav.links.map((link, index) => (
									<Link key={index} to={`/${link.slug.current === "home" ? "" : link.slug.current}`} className="hover:text-secondary w-fit">
										<Typography weight="light" size="sm">
											{link.name}
										</Typography>
									</Link>
								))}
							</div>
						</div>
					))}
				</CollapsibleContent>
			</Collapsible>

			<div className="flex flex-col items-center p-3 bg-foreground text-muted">
				<div className="w-full flex flex-col gap-5 md:flex-row justify-between px-5 md:px-10">
					{/* <Logo className="size-10 scale-150 hidden md:block" /> */}

					<p className="text-center">
						<a href="/privacy-policy" className="hover:text-secondary">Privacy Policy</a>
					</p>

					<p className="my-auto text-center">© {year} Manipal Business Solutions. All rights reserved.</p>

					<div className="flex gap-5 justify-center my-auto">
						<Logo className="size-16 block md:hidden" />
						{socials.map((social, i) => (
							<div key={i} className="flex justify-center items-end my-auto">
								<a href={social.url} className="text-primary">
									<SVG src={social.icon.svg} className="size-6" style={{ color: social.iconColor.hex }} title={social.name} />
								</a>
							</div>
						))}
					</div>
				</div>
			</div>
		</footer>

	);
}
