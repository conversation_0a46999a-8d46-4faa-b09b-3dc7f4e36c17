import { Nav } from '@/layouts';
import React, { useState } from 'react';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { ArrowRight, ChevronDown, ChevronLeft, ChevronRight, ChevronsUp } from 'lucide-react';
import { Link } from 'gatsby';
import { Arrow } from '@radix-ui/react-hover-card';

type NavChildProps = {
	navs: Nav[];
	multiColumn: boolean;
	closeHoverCard: () => void;
}

interface ChildNav extends Nav {
	parentSlug?: string;
}

const NavChild: React.FC<NavChildProps> = ({ navs, multiColumn, closeHoverCard }) => {
	const cols = multiColumn ? navs.length : 1;
	const colsTemplate = `repeat(${cols}, 1fr)`;

	const rows = multiColumn ? Math.ceil(navs.reduce((acc, nav) => Math.max(nav.children.length, acc), 0) / cols) : navs.length;
	const rowsTemplate = `repeat(${rows}, 1fr)`;

	const SingleChild = ({ name, slug, parentSlug }: ChildNav) => {
		const link = false ?//parentSlug ?
			'/' + parentSlug + '/' + slug.current : '/' + slug.current;
		return (
			<li>
				<Link
					to={link}
					className='flex relative select-none space-y-1 p-3 leading-none no-underline outline-none transition-all group hover:text-secondary [&.active]:text-primary hover:scale-105 duration-300 ease-in-out hover:before:h-full before:absolute before:top-0 before:left-0 before:h-0 before:w-1 before:bg-secondary before:rounded-l before:transition-all before:duration-300 before:ease-in-out hover:bg-gradient-to-r from-muted to-transparent rounded'
					activeClassName='active'
					onClick={closeHoverCard}
				>
					<p className={`text-base font-medium leading-none flex flex-col group-[.active]:gap-2 after:h-0 after:w-1/2 after:opacity-0 after:bg-primary group-[.active]:after:opacity-100 group-[.active]:after:h-0.5`}>
						{name}
					</p>
				</Link>
			</li>
		)
	}

	const NestedParent = ({ name, slug, children }: Nav) => {
		return (
			<li>
				<HoverCard openDelay={100} closeDelay={100}>
					<HoverCardTrigger className='group'>
						<p className='flex select-none space-y-1 p-3 leading-none no-underline outline-none transition-all group-data-[state=open]:scale-105 group-data-[state=open]:text-secondary duration-300 ease-in-out group-data-[state=open]:before:h-full before:absolute before:top-0 before:left-0 before:h-0 before:w-1 before:bg-secondary before:rounded-l before:transition-all before:duration-300 before:ease-in-out group-data-[state=open]:bg-gradient-to-r from-muted to-transparent rounded'>
							<span className={`text-base font-medium leading-none relative w-full pr-4`}>
								{name}
								<ChevronRight className='absolute right-0 top-1/2 bottom-1/2 -translate-y-1/2 translate-x-1/2 text-foreground/30 group-data-[state=open]:-scale-x-100 transition-all duration-300 ease-in-out group-data-[state=open]:delay-150'/>
							</span>
						</p>
					</HoverCardTrigger>
					<HoverCardContent side='right' className='w-full' sideOffset={10} align='start'>
						<Arrow className='fill-popover' />
						<ul className={`grid`}>
							{
								children.map((child, i) => <SingleChild key={i} parentSlug={slug.current} {...child} />)
							}
						</ul>
					</HoverCardContent>
				</HoverCard>
			</li>
		)
	}

	const isNested = navs.some(nav => nav.children.length > 0);

	return (
		<div className={`max-w-lg flex flex-col`}>
			<ul className={`grid gap-x-4`} style={{
				gridTemplateColumns: multiColumn && navs.length > 3 ? 'repeat(2, 1fr)' : '',
				// gridTemplateRows: rowsTemplate,
				// gridAutoFlow: 'column'
			}}>
				{
					isNested ? navs.map((nav, i) => <NestedParent key={i} {...nav} />) :
						navs.map((nav, i) => <SingleChild key={i} {...nav} />)
				}
			</ul>
		</div>
	)
}

const NavHoverCard: React.FC<Nav> = ({ name, slug, multiColumn, children }) => {
	const [open, setOpen] = useState<boolean>(false);

	const homeSlug = slug.current === 'home';

	if (children.length === 0) return (
		<Link to={homeSlug ? '/' : '/' + slug.current} className='nav-item group hover:text-secondary transition-colors duration-300 ease-in-out text-base'>
			{name}
			{/* <div className='w-full'>
				<ChevronsUp size={24} className='group-hover:opacity-100 group-hover:-bottom-0 text-primary ease-in-out transition-all duration-300' />
			</div> */}
		</Link>
	)

	function closeHoverCard() {
		setOpen(false);
	}

	return (
		<HoverCard openDelay={100} closeDelay={100} open={open} onOpenChange={setOpen}>
			<HoverCardTrigger asChild>
				<p className='relative mx-8 my-auto group data-[state=open]:text-secondary transition-colors duration-300 ease-in-out text-base font-semibold text-center flex'>
					{name}
					{/* <ChevronDown size={24} className='group-data-[state=open]:rotate-180 ease-in-out transition-all duration-300 text-foreground' /> */}
					{/* <div className='w-full absolute h-6 flex flex-col items-center justify-center'>
                        <ChevronsUp size={24} className='absolute group-data-[state=open]:opacity-100 group-data-[state=open]:-bottom-0 opacity-0 -bottom-4 text-primary ease-in-out transition-all duration-300' />
                    </div> */}
				</p>
			</HoverCardTrigger>
			<HoverCardContent className='w-full' side='top'>
				<Arrow className='fill-popover drop-shadow-sm w-4 h-2' />
				<NavChild navs={children} multiColumn={multiColumn} closeHoverCard={closeHoverCard} />
			</HoverCardContent>
		</HoverCard>
	)
}

export default NavHoverCard;