import { SEO } from "@/components/layout/seo";
import { pageQueryResult } from "@/components/sections/sanityTypes";
import { graphql, type HeadFC, type PageProps } from "gatsby";


import Logo from "@/images/Logo.svg";

import React, { useState } from "react";

const IndexPage: React.FC<PageProps<pageQueryResult>> = ({ data }) => {

	return (
		<div className="w-full">
			
			{/* <div className="pt-20"> */}
			<div>
				<div className="thank-you-banner-bg">
					<svg xmlns="http://www.w3.org/2000/svg" width="1440" height="400" viewBox="0 0 1440 400">
						<defs>
							<clipPath id="clipPath8775740876">
								<path d="M0 0L1440 0L1440 400L0 400L0 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 0 0)"/>
							</clipPath>
						</defs>
						<g clip-path="url(#clipPath8775740876)">
							<rect width="1440" height="400" transform="matrix(1 0 0 1 0 0)" fill="rgb(255, 255, 255)"/>
							<defs>
								<clipPath id="clipPath5680856417">
									<path d="M0 0L1440 0L1440 400L0 400L0 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 0 0)"/>
								</clipPath>
							</defs>
							<g clip-path="url(#clipPath5680856417)">
								<defs>
									<clipPath id="clipPath3223152878">
										<path d="M0 0L1440 0L1440 900L0 900L0 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 0 0)"/>
									</clipPath>
								</defs>
								<g clip-path="url(#clipPath3223152878)">
									<rect width="1440" height="400" transform="matrix(1 0 0 1 0 0)" fill="rgb(245, 248, 255)"/>
									<defs>
										<clipPath id="clipPath9413533943">
											<path d="M0 0L1440 0L1440 200L0 200L0 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 0 200)"/>
										</clipPath>
									</defs>
									<g clip-path="url(#clipPath9413533943)">
										<path d="M0 14.4338C240 64.4338 480 -35.5662 720 14.4338C960 64.4338 1200 -35.5662 1440 14.4338L1440 164.434L0 164.434L0 14.4338Z" fill-rule="nonzero" transform="matrix(1 0 0 1 0 235.566)" fill="rgb(240, 245, 255)"/>
									</g>
									<g opacity="0.05">
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 873.188 35.7344)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 641.328 497.609)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 272.781 706.828)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 940.141 460.859)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 1143.75 107.25)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 632.219 517.547)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 498.703 10.2031)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 232.672 134.453)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 343.547 592.266)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 1393.09 794.625)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 1339.78 797.453)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 10.4062 382.266)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 1245.91 141.875)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 8.96875 828.906)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 67.6562 343.344)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 566.547 466.625)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 556.031 241.594)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 391.594 517.062)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 856.719 484.172)" fill="rgb(34, 76, 147)"/>
										<path d="M4 0C1.79086 0 0 1.79086 0 4L0 4.006C0 6.21514 1.78486 8 3.994 8L4 8C6.20914 8 8 6.21514 8 4.006L8 4C8 1.79086 6.21514 0 4.006 0L4 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 1020.59 880.344)" fill="rgb(34, 76, 147)"/>
									</g>
								</g>
							</g>
						</g>
					</svg>
				</div>
				<div className="thankyou-logo">
					<Logo className="size-20 origin-left scale-125" />
				</div>
				<div className="thankyou-well">
					<div className="thankyou-icon">
						<svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 80 80">
							<path d="M40 0C17.9086 0 0 17.9086 0 40L0 40.06C0 62.1514 17.8486 80 39.94 80L40 80C62.0914 80 80 62.1514 80 40.06L80 40C80 17.9086 62.1514 0 40.06 0L40 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 0 0)" fill="rgb(240, 245, 255)"/>
							<defs>
								<clipPath id="clipPath8425685201">
									<path d="M0 0L48 0L48 48L0 48L0 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 16 16)"/>
								</clipPath>
							</defs>
							<g clip-path="url(#clipPath8425685201)">
								<path d="M19.5 0C8.74781 0 0 8.74781 0 19.5C0 30.2522 8.74781 39 19.5 39C30.2522 39 39 30.2522 39 19.5C39 8.74781 30.2522 0 19.5 0ZM29.6484 12.9647L17.0484 27.9647C16.9086 28.1312 16.7405 28.2612 16.5441 28.3547C16.3478 28.4481 16.1409 28.4965 15.9234 28.5L15.8981 28.5C15.6855 28.4999 15.4821 28.4566 15.288 28.37C15.0938 28.2835 14.9256 28.1612 14.7834 28.0031L9.38344 22.0031C9.09461 21.6968 8.95883 21.3333 8.97609 20.9126C8.99336 20.4919 9.15847 20.1408 9.47144 19.8591C9.7844 19.5775 10.151 19.4502 10.5711 19.4772C10.9913 19.5042 11.3385 19.6775 11.6128 19.9969L15.8588 24.7144L27.3516 11.0353C27.6198 10.7252 27.9582 10.5539 28.3669 10.5213C28.7756 10.4888 29.1369 10.6043 29.4509 10.868C29.7648 11.1317 29.941 11.4677 29.9795 11.8759C30.0179 12.284 29.9076 12.647 29.6484 12.9647Z" fill-rule="nonzero" transform="matrix(1 0 0 1 20.5 20.5)" fill="#2d90e1"/>
							</g>
						</svg>
					</div>
					<h5>Thank You!</h5>
					<h6>Thanks for sharing your details.</h6>
					<p>One of our team members will be in touch with you shortly. In the meantime, feel free to browse our website or check out our brochure to get a better idea of what we do.</p>
					<div className="thankyou-actions">
						<a href="/" className="primary-button">
							<span>Explore More</span>
							<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
								<defs>
									<clipPath id="clipPath1681861640">
										<path d="M0 0L20 0L20 20L0 20L0 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 0 0)"/>
									</clipPath>
								</defs>
								<g clip-path="url(#clipPath1681861640)">
									<path d="M5.89959 0.662913C5.53347 0.296799 5.53347 -0.296799 5.89959 -0.662913C6.2657 -1.02903 6.8593 -1.02903 7.22541 -0.662913L12.8504 4.96209Q12.9157 5.02738 12.967 5.10415Q13.0183 5.18093 13.0536 5.26623Q13.089 5.35154 13.107 5.4421Q13.125 5.53266 13.125 5.625Q13.125 5.71733 13.107 5.8079Q13.089 5.89846 13.0536 5.98376Q13.0183 6.06907 12.967 6.14585Q12.9157 6.22262 12.8504 6.28791L7.22541 11.9129C6.8593 12.279 6.2657 12.279 5.89959 11.9129C5.53347 11.5468 5.53347 10.9532 5.89959 10.5871L9.92418 6.5625L0 6.5625C-0.517762 6.5625 -0.9375 6.14276 -0.9375 5.625C-0.9375 5.10724 -0.517762 4.6875 0 4.6875L9.92418 4.6875L5.89959 0.662913Z" fill-rule="evenodd" transform="matrix(1 0 0 1 3.90625 4.375)" fill="rgb(255, 255, 255)"/>
								</g>
							</svg>
						</a>
						<a href="https://mbsweb.banksekure.com/Attachments/CKYC_brochure.pdf" target="blank" className="secondary-button">
							<span>Download Brochure</span>
							<svg xmlns="http://www.w3.org/2000/svg" width="19.7656" height="19.7656" viewBox="0 0 19.7656 19.7656">
								<defs>
									<clipPath id="clipPath3819134179">
										<path d="M0 0L19.7656 0L19.7656 19.7656L0 19.7656L0 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 -0.******** 0)"/>
									</clipPath>
								</defs>
								<g clip-path="url(#clipPath3819134179)">
									<path d="M11.427 4.88349L7.4121 4.88349L7.4121 10.7349L9.44618 8.72505C9.56761 8.61104 9.71205 8.5551 9.87953 8.55722C10.047 8.55934 10.1899 8.61892 10.3084 8.73596C10.4268 8.853 10.4871 8.99428 10.4892 9.15979C10.4914 9.3253 10.4348 9.46805 10.3194 9.58805L7.23104 12.6402C7.11045 12.7593 6.96491 12.8189 6.79443 12.8189C6.62394 12.8189 6.4784 12.7593 6.35781 12.6402L3.26943 9.58805C3.15407 9.46805 3.09747 9.3253 3.09961 9.15979C3.10176 8.99428 3.16204 8.853 3.28047 8.73596C3.3989 8.61892 3.54185 8.55934 3.70933 8.55722C3.8768 8.5551 4.02125 8.61104 4.14267 8.72505L6.17675 10.7349L6.17675 4.88349L2.16186 4.88349C1.56515 4.88412 1.05582 5.09294 0.633872 5.50994C0.211929 5.92694 0.000638542 6.4303 0 7.02002L0 14.9557C0.000638149 15.5454 0.211929 16.0488 0.633872 16.4658C1.05582 16.8828 1.56515 17.0916 2.16186 17.0922L11.427 17.0922C12.0237 17.0916 12.533 16.8828 12.955 16.4658C13.3769 16.0488 13.5882 15.5454 13.5889 14.9557L13.5889 7.02002C13.5882 6.4303 13.3769 5.92693 12.955 5.50994C12.533 5.09294 12.0237 4.88412 11.427 4.88349ZM7.4121 0.610437C7.4121 0.441869 7.3518 0.297988 7.23119 0.178793C7.11058 0.0595976 6.96499 0 6.79443 0C6.62386 0 6.47827 0.0595975 6.35766 0.178793C6.23705 0.297988 6.17675 0.441869 6.17675 0.610437L6.17675 4.88349L7.4121 4.88349L7.4121 0.610437Z" fill-rule="nonzero" transform="matrix(1 0 0 1 3.08826 1.33669)" fill="#2d90e1"/>
								</g>
							</svg>
						</a>
					</div>
				</div>
				
			</div>
			

			
		</div>
	);
};

export default IndexPage;

export const Head: HeadFC<pageQueryResult> = ({ data }) => <SEO seo={data.allSanityPage.nodes[0].seo} />;

export const query = graphql`
	query PageQuery {
		allSanityPage(filter: { slug: { current: { eq: "thank-you-page" } } }) {
			nodes {
				title
				seo {
					title
					description
					keywords
					canonicalUrl
					ogTitle
					ogDescription
					ogImage {
						asset {
							publicUrl
							url
						}
					}
					noindex
					structuredData
				}
				hero {
					title
					subtitle
					cta
					ctaIcon {
						svg
					}
					ctaLink
					bgImage {
						asset {
							gatsbyImageData
							url
							publicUrl
						}
						alt
						title
					}
					imageAlign
				}
				sections {
					title
					subtitle
					id
					bg
					statistics {
						titleString
						titleNumber
						subtitle
						type
						icon {
							svg
						}
						image {
							asset {
								gatsbyImageData
								url
								publicUrl
							}
							alt
							title
						}
					}
					content {
						type
						content {
							title
							content: _rawContent
							cta
							ctaIcon {
								svg
							}
							ctaLink
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							imageAlign
							imageAsBg
						}
						
						bigList {
							title
							description: _rawDescription
						}
						carousel {
							type
							delay
							contentType
							iconCards {
								type
								image {
									asset {
										gatsbyImageData
										url
										publicUrl
									}
									alt
									title
								}
								icon {
									svg
								}
								title
								description: _rawDescription
							}
							carousel {
								title
								content: _rawContent
								cta
								ctaIcon {
									svg
								}
								ctaLink
								image {
									asset {
										gatsbyImageData
										url
										publicUrl
									}
									alt
									title
								}
								imageAlign
								imageAsBg
							}
						}
						iconCards {
							title
							type
							icon {
								svg
							}
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							description: _rawDescription
						}
						icons {
							title
							type
							icon {
								svg
							}
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
						}
						offices {
							name
							description
							address
							phones
							fax
							email
							location {
								lat
								lng
							}
						}
						employees {
							name
							position
							profile: _rawProfile
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
						}
						product {
							title
							cta
							ctaLink
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							highlights: _rawHighlights
							description: _rawDescription
						}
						tabs {
							title
							content {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								columns {
									splitRatio
									sticky
									contentLeft {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
									contentRight {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
						}
						columns {
							splitRatio
							sticky
							contentLeft {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								tabs {
									title
									content {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
							contentRight {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								tabs {
									title
									content {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
`;
