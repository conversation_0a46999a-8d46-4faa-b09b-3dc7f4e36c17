import ContentHandler from "@/components/sections/ContentHandler";
import { ColumnProps } from "@/components/sections/sanityTypes";
import React from "react";

function getSplitClass(splitRatio: string) {
    switch (splitRatio) {
        case "1/3":
            return { splitClassLeft: "md:w-1/2 lg:w-1/3", splitClassRight: "md:w-1/2 lg:w-2/3" }
        case "2/3":
            return { splitClassLeft: "md:w-1/2 lg:w-2/3", splitClassRight: "md:w-1/2 lg:w-1/3" }
        case "1/4":
            return { splitClassLeft: "md:w-1/2 lg:w-1/4", splitClassRight: "md:w-1/2 lg:w-3/4" }
        case "3/4":
            return { splitClassLeft: "md:w-1/2 lg:w-3/4", splitClassRight: "md:w-1/2 lg:w-1/4" }
        case "1/5":
            return { splitClassLeft: "md:w-1/2 lg:w-1/5", splitClassRight: "md:w-1/2 lg:w-4/5" }
        case "2/5":
            return { splitClassLeft: "md:w-1/2 lg:w-2/5", splitClassRight: "md:w-1/2 lg:w-3/5" }
        case "3/5":
            return { splitClassLeft: "md:w-1/2 lg:w-3/5", splitClassRight: "md:w-1/2 lg:w-2/5" }
        case "4/5":
            return { splitClassLeft: "md:w-1/2 lg:w-4/5", splitClassRight: "md:w-1/2 lg:w-1/5" }
        case "1/6":
            return { splitClassLeft: "md:w-1/2 lg:w-1/6", splitClassRight: "md:w-1/2 lg:w-5/6" }
        case "5/6":
            return { splitClassLeft: "md:w-1/2 lg:w-5/6", splitClassRight: "md:w-1/2 lg:w-1/6" }
        case "5/12":
            return { splitClassLeft: "md:w-1/2 lg:w-5/12", splitClassRight: "md:w-1/2 lg:w-7/12" }
        case "7/12":
            return { splitClassLeft: "md:w-1/2 lg:w-7/12", splitClassRight: "md:w-1/2 lg:w-5/12" }
        default:
            // 1/2 split by default
            return { splitClassLeft: "md:w-1/2", splitClassRight: "md:w-1/2" }
    }
}

function getStickyClass(sticky: "none" | "left top" | "right top" | "left bottom" | "right bottom", side: 'left' | 'right') {
    if (!sticky || sticky === "none") return "";

    const topSticky = "top-0";
    const bottomSticky = "bottom-0";

    if (sticky.includes(side)) {
        return `md:h-screen md:sticky ${topSticky} md:flex md:jusitfy-center md:items-center`;
    }

    return "";
}

const Columns: React.FC<ColumnProps> = ({ splitRatio, contentLeft, contentRight, sticky = 'none', reverseOrderOnMobile = false }) => {
    const { splitClassLeft, splitClassRight } = getSplitClass(splitRatio);

    const leftStickyClass = getStickyClass(sticky, 'left');
    const rightStickyClass = getStickyClass(sticky, 'right');

    const StickyWrapper = ({ stickyClass, children }: { stickyClass: string, children: React.ReactNode }) => stickyClass ?
        <div className="h-fit">{children}</div> :
        <>{children}</>

    return (
        <div className={`flex ${reverseOrderOnMobile ? "flex-col-reverse" : "flex-col"} md:flex-row md:gap-8`}>
            <div className={`w-full ${leftStickyClass} ${splitClassLeft} relative translate-y-4 md:translate-y-0 pb-4 md:pb-0`}>
                <StickyWrapper stickyClass={leftStickyClass}>
                    <ContentHandler {...contentLeft} />
                </StickyWrapper>
            </div>

            <div className={`w-full ${rightStickyClass} ${splitClassRight} z-[1]`}>
                <StickyWrapper stickyClass={rightStickyClass}>
                    <ContentHandler {...contentRight} />
                </StickyWrapper>
            </div>
        </div >
    )
}

export default Columns;