import { SEO } from "@/components/layout/seo";
import HeroSection from "@/components/sections/hero";
import { HeroSectionProps, NewsCardProps, SEOData } from "@/components/sections/sanityTypes";
import Section from "@/components/sections/section";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Typography } from "@/components/ui/typography";
import { graphql, HeadFC, PageProps } from "gatsby";
import { GatsbyImage } from "gatsby-plugin-image";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination"
import React, { useEffect, useState } from "react";

type NewsQueryResult = {
    allSanityPage: {
        nodes: {
            seo: SEOData;
            hero: HeroSectionProps;
        }[];
    },
    allSanityNews: {
        nodes: NewsCardProps[];
    }
}

const NewsCard: React.FC<NewsCardProps> = ({ title, date, type, image, link }) => {
    const imageAlt = image?.alt || title || 'image'
    const imageTitle = image?.title || title || 'image'

    return (
        <>
            <div className="flex gap-8">
                <Card className="h-60 w-60 p-6 flex justify-center items-center">
                    <GatsbyImage
                        image={image.asset.gatsbyImageData}
                        alt={imageAlt}
                        title={imageTitle}
                        objectFit="contain"
                        // className="h-full w-full"
                    // objectPosition={'bottom'}
                    />
                </Card>
                <a
                    href={link}
                    className="flex-1"
                    target="_blank"
                    rel="noopener noreferrer"
                >
                    <Card className="p-6 h-full group flex flex-col justify-evenly hover:scale-105 duration-150">
                        <Typography size="sm" weight='light' className="flex">
                            {type}
                            <Separator orientation='vertical' className="mx-2 w-0.5" />
                            {date}
                        </Typography>
                        <Typography size="xl" component='h2' className="mt-2 group-hover:text-secondary duration-200">
                            {title}
                        </Typography>
                        <Typography className="text-secondary mt-2">
                            Read More
                        </Typography>
                    </Card>
                </a>
            </div>
            <img src="../images/Tax Relief, Reforms, And Support For Innovation Culture.jpg" alt="" />
        </>
    )
}

const Page: React.FC<PageProps<NewsQueryResult>> = ({ data }) => {
    const hero = data.allSanityPage.nodes[0].hero;
    const news = data.allSanityNews.nodes;

    const itemsPerPage = 5;
    const totalPages = Math.ceil(news.length / itemsPerPage)
    const maxPagesToShow = 5; // Maximum number of pages to display


    const [page, setPage] = useState(0);
    const [pageArray, setPageArray] = useState<(number | string)[]>([]);

    const handlePageChange = (index: number) => {
        setPage(index);
    }

    // Calculate the pagination array based on page
    const calculatePageArray = () => {
        let startPage: number, endPage: number;

        if (totalPages <= maxPagesToShow) {
            // Show all pages if total pages are fewer than or equal to maxPagesToShow
            return Array.from({ length: totalPages }, (_, i) => i + 1);
        }

        // Otherwise, calculate range of pages to display with ellipsis
        if (page <= 3) {
            startPage = 1;
            endPage = maxPagesToShow;
        } else if (page + 2 >= totalPages) {
            startPage = totalPages - maxPagesToShow + 1;
            endPage = totalPages;
        } else {
            startPage = page - 2;
            endPage = page + 2;
        }

        // Add ellipses where necessary
        const pageNumbers = [];
        if (startPage > 1) {
            pageNumbers.push(1); // First page
            if (startPage > 2) pageNumbers.push('...');
        }
        for (let i = startPage; i <= endPage; i++) {
            pageNumbers.push(i);
        }
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) pageNumbers.push('...');
            pageNumbers.push(totalPages); // Last page
        }

        return pageNumbers;
    };

    // Update pageArray when currentPage or totalPages changes
    useEffect(() => {
        setPageArray(calculatePageArray());
    }, [page]);

    const PaginationComponent = () => (
        <Pagination className="my-10">
            <PaginationContent>
                <PaginationItem>
                    <PaginationPrevious disabled={page == 0} onClick={() => handlePageChange(page - 1)} />
                </PaginationItem>
                {pageArray.map((item, index) => <PaginationItem>
                    {typeof item === "number" ? <PaginationLink onClick={() => handlePageChange(item - 1)} isActive={page==item-1}>
                        {item}
                    </PaginationLink> :
                        <PaginationEllipsis />}
                </PaginationItem>)}
                <PaginationItem>
                    <PaginationNext disabled={page == totalPages - 1} onClick={() => handlePageChange(page + 1)} />
                </PaginationItem>
            </PaginationContent>
        </Pagination>
    )

    return (
        <div className="w-full">
            <HeroSection {...hero} />

            <Section title="" subtitle="" id="news" bg="default" statistics={[]} last>
                <PaginationComponent />
                <div className="flex flex-col gap-8">
                    {news.slice(page*itemsPerPage, (page+1)*itemsPerPage).map((news, index) => (
                        <NewsCard key={index} {...news} />
                    ))}
                </div>
                <PaginationComponent />
            </Section>
        </div>
    )
}

export const Head: HeadFC<NewsQueryResult> = ({ data }) => <SEO seo={data.allSanityPage.nodes[0].seo} />

export default Page;

export const query = graphql`
  query NewsQuery {
    allSanityNews(sort: {date: DESC}) {
      nodes {
        title
        type
        date
        link
        image {
          asset {
            gatsbyImageData
          }
          title
          alt
        }
      }
    }
    allSanityPage(filter: {slug: {current: {eq: "news"}}}) {
      nodes {
        seo {
          title
          description
          keywords
          canonicalUrl
          ogTitle
          ogDescription
          ogImage {
            asset {
              publicUrl
              url
            }
          }
          noindex
          structuredData
        }
        hero {
          title
          subtitle
          cta
          ctaIcon {
            svg
          }
          ctaLink
          bgImage {
            asset {
              gatsbyImageData
              url
              publicUrl
            }
            alt
            title
          }
          imageAlign
        }
      }
    }
  }
`