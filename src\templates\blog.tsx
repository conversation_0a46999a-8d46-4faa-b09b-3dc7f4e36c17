import { SEO } from "@/components/layout/seo";
import BlogComponent from "@/components/sections/Blog";
import { BlogProps } from "@/components/sections/sanityTypes";
import { graphql, HeadFC } from "gatsby";
import React from "react";

type BlogPageQueryResult = {
    allSanityBlog: {
        nodes: BlogProps[];
    }
}

const BlogPageTemplate: React.FC<{ data: BlogPageQueryResult }> = ({ data }) => {
    return <BlogComponent {...data.allSanityBlog.nodes[0]} />
}

export const Head: HeadFC<BlogPageQueryResult> = ({ data }) => <SEO seo={data.allSanityBlog.nodes[0].seo} />

export default BlogPageTemplate;

export const query = graphql`
  query BlogPageQuery($slug: String!) {
    allSanityBlog(filter: {slug: {current: {eq: $slug}}}) {
      nodes {
        date: _createdAt
        title
      seo {
        title
        description
        keywords
        canonicalUrl
        ogTitle
        ogDescription
        ogImage {
          asset {
            publicUrl
            url
          }
        }
        noindex
        structuredData
      }
        slug { current }
        content: _rawContent
        image {
            asset {
            gatsbyImageData
            }
            title
            alt
        }
      }
    }
  }
`