import NavHoverCard from "@/components/layout/NavHoverCard";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Sheet, SheetClose, SheetContent, SheetT<PERSON>le, SheetTrigger } from "@/components/ui/sheet";
import Logo from "@/images/Logo.svg";
import { Nav } from "@/layouts";
import * as VisuallyHidden from "@radix-ui/react-visually-hidden";
import { useLocation } from "@reach/router";
import { Link } from "gatsby";
import { ArrowRight, CircleArrowRight, Menu, X } from "lucide-react";
import React, { useEffect, useState } from "react";

export default function Header({ navigation }: { navigation: Nav[] }) {
	const navbarHeight = 96;
	const [scrollDirection, setScrollDirection] = useState<"down" | "up">("up");

	const [sheetOpen, setSheetOpen] = useState(false);
	const { pathname } = useLocation();

	// hides navbar on scroll down, shows on scroll up
	useEffect(() => {
		let lastScrollY = window.scrollY;
		const updateScrollDirection = () => {
			const scrollY = window.scrollY;
			const dir = scrollY > lastScrollY ? "down" : "up";
			// console.log(scrollY, lastScrollY, dir);
			if (lastScrollY !== scrollY && scrollDirection !== dir && Math.abs(scrollY - lastScrollY) > 1) {
				if ((scrollY <= navbarHeight && dir === "up") || scrollY > navbarHeight) setScrollDirection(dir);
			}
			lastScrollY = scrollY;
		};
		window.addEventListener("scroll", updateScrollDirection);
		return () => window.removeEventListener("scroll", updateScrollDirection);
	}, [scrollDirection]);

	useEffect(() => {
		setSheetOpen(false);
	}, [pathname]);

	return (
		<header className={`shadow-xl fixed w-full ${scrollDirection === "down" ? "-top-24" : "top-0"} h-20 bg-white z-[49] transition-all duration-500 overflow-hidden`}>
			<div className="mx-auto p-2 flex justify-between items-center">
				{/* Logo */}
				<Link to="/" className="-m-2 ml-4">
					<Logo className="size-20 origin-left scale-125" />
				</Link>

				{/* PC Nav */}
				<nav key={1} className="lg:flex hidden text-foreground">
					{navigation.map((nav, i) => (
						<NavHoverCard {...nav} key={i} />
					))}
					<div className="mx-8 text-lg font-semibold my-auto">
						<Link to="/get-in-touch">
							<Button size="sm">
								Contact Us <CircleArrowRight className="h-5 w-5" />
							</Button>
						</Link>
					</div>
				</nav>

				{/* Mobile Nav */}
				<Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
					<VisuallyHidden.Root>
						<SheetTitle>Menu</SheetTitle>
					</VisuallyHidden.Root>
					<SheetTrigger className="mr-5 lg:hidden">
						{/* <Button variant='ghost' size='icon' className='shrink-0 md:hidden'> */}
						<Menu className="" />
						<span className="sr-only">Open Navigation</span>
						{/* </Button> */}
					</SheetTrigger>
					<SheetContent side="right" className="flex flex-col w-full">
						<div className="flex justify-between items-center">
							<Logo className="ml-5 h-10 scale-[2]" />
							<SheetClose>
								<X className="h-10 w-10" />
							</SheetClose>
						</div>
						<Separator className="my-2" />
						<nav className="grid gap-5 font-medium">
							<Accordion type="single" collapsible>
								{navigation.map((nav, i) =>
									nav.children.length > 0 ? (
										<AccordionItem key={i} value={nav.name}>
											<AccordionTrigger className="">{nav.name}</AccordionTrigger>
											<AccordionContent>
												<div className="grid gap-2">
													{nav.children.map((child, j) => (
														<React.Fragment key={j}>
															<div className="flex gap-2">
																{child.children.length > 0 ? (
																	<p className="font-normal flex flex-col gap-1 after:h-0.5 after:w-full after:bg-primary">{child.name}</p>
																) : (
																	<>
																		<ArrowRight className="text-primary" />
																		<Link className={`font-light ${pathname.includes(child.slug.current) && "text-secondary"}`} to={"/" + child.slug.current}>
																			{child.name}
																		</Link>
																	</>
																)}
															</div>
															{child.children.length > 0 &&
																child.children.map((subChild, k) => (
																	<div key={k} className="flex gap-2 pl-5">
																		<ArrowRight className="text-primary" />
																		<Link className={`font-light ${pathname.includes(subChild.slug.current) && "text-secondary"}`} to={"/" + subChild.slug.current}>
																			{subChild.name}
																		</Link>
																	</div>
																))}
														</React.Fragment>
													))}
												</div>
											</AccordionContent>
										</AccordionItem>
									) : (
										nav.slug.current !== "home" && (
											<Link key={i} to={"/" + nav.slug.current}>
												{nav.name}
											</Link>
										)
									)
								)}
							</Accordion>
						</nav>

						<div className="mt-auto p-3">
							<Link to="/get-in-touch">
								<Button size="sm" className="w-full text-xl">
									Contact Us <CircleArrowRight className="h-5 w-5 stroke-[3]" />
								</Button>
							</Link>
						</div>
					</SheetContent>
				</Sheet>
			</div>
		</header>
	);
}
