import React from "react";
import { twMerge } from "tailwind-merge";

interface SlidingNavProps {
    fade?: boolean;
    left?: boolean;
    className?: string;
    titleClassName?: string;
    sliderClassName?: string;
    style?: React.CSSProperties;
    sliderStyle?: React.CSSProperties;
    sliderChildren?: React.ReactNode;
    title: React.ReactNode;
}

export default function SlidingNav({ fade=false, left=false, className, titleClassName, sliderClassName, sliderChildren, style, sliderStyle, title, children }: React.PropsWithChildren<SlidingNavProps>) {
    const getSkew = (left: boolean) => left ? `skew-y-12` : `-skew-y-12`;

    const slideAnim = fade ? "opacity-0 group-hover:opacity-100" : "scale-x-0 group-hover:scale-x-100";

    return <div style={style} className={twMerge(className, `group relative overflow-hidden  transition-all duration-200 ${getSkew(left)} shadow-center-3xl flex justify-center items-center`)}>
        <div className={`absolute h-full w-full ${getSkew(!left)}`}>
            {children}
        </div>
        <div className={twMerge(sliderClassName, `absolute left-0 top-0 w-full h-full ease-out transition-all duration-300 ${slideAnim} group-hover:origin-left origin-right`)} style={sliderStyle}>
            {sliderChildren}
        </div>
        <p className={twMerge(titleClassName, `text-3xl font-semibold ${getSkew(!left)} group-hover:drop-shadow-outline transition-all duration-300`)}>{title}</p>
    </div>;
}