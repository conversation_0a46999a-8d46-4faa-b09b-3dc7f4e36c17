import { SEO } from "@/components/layout/seo";
import { pageQueryResult } from "@/components/sections/sanityTypes";
import { SectionLower } from "@/components/sections/sectionDividers";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Typography } from "@/components/ui/typography";
import { graphql, HeadFC, PageProps } from "gatsby";
import React from "react";

const Page: React.FC<PageProps<pageQueryResult>> = ({}) => {
	return (
		<div className="w-full">
			<section className="py-20 px-5">
				<div className="pt-20">
					<div>
						<Typography className="text-center" size="xl3" component="h1">
							PRIVACY <span className="text-secondary">POLICY</span>
						</Typography>
						
						<Typography className="mt-10">Manipal Business Solutions Private Limited (“us”, “we”, or “our”) operates the <a href="https://manipalbusinesssolutions.com/" className="text-secondary">https://manipalbusinesssolutions.com/</a>. </Typography>

						<Typography className="mt-5">This Privacy Policy explains Manipal Business Solutions Private Limited (“MBS”) collects, uses, shares and protects information of users (“You”) through its website located at the URL “<a href="https://manipalbusinesssolutions.com/" className="text-secondary">https://manipalbusinesssolutions.com/</a>” (“Platform”). </Typography>

						<Typography className="mt-5">MBS is committed to protecting your privacy and has therefore, provided this Policy to familiarize You with the manner in which MBS uses the information disclosed by You to MBS.</Typography>

						<Typography className="mt-5">The terms of the Policy shall govern your use of the Platform. This Policy shall be construed in compliance with Information Technology Act, 2000 as amended from time to time and read with Information Technology (Reasonable Security Practices and Procedures and Sensitive Personal Data or Information) Rules, 2011.</Typography>

						<Typography className="mt-5">WE USE YOUR DATA TO PROVIDE AND IMPROVE THE SERVICE. BY ACCESSING AND USING THE PLATFORM AND AVAILING THE SERVICES OFFERED BY MBS THROUGH THE PLATFORM, YOU AGREE TO THE COLLECTION AND USE OF INFORMATION IN ACCORDANCE WITH THIS POLICY. UNLESS OTHERWISE DEFINED IN THIS PRIVACY POLICY, TERMS USED IN THIS PRIVACY POLICY HAVE THE SAME MEANINGS AS IN OUR TERMS AND CONDITIONS, ACCESSIBLE FROM <a href="https://manipalbusinesssolutions.com/" className="text-secondary">WWW.MANIPALBUSINESSSOLUTIONS.COM</a>  YOU ARE RESIDING IN THE EUROPEAN UNION, PLEASE NOTE THAT, BY FURTHER USING THIS PLATFORM, YOU AGREE THAT YOUR CONSENT IS GIVEN TO US TO PROCESS YOUR PERSONAL DATA. </Typography>

						<Typography className="mt-5">MBS reserves the right to change, modify or remove portions of this Policy at any time. MBS recommends that You review this Policy periodically to ensure that You are aware of the current privacy practices. MBS may notify You of the modifications to the Policy on Your use of the Platform or in any other manner as MBS may deem fit. Your continued use of the Platform after the amendment to the Policy will be deemed acceptance of the modifications thereof.</Typography>

						
						<ul className="mt-2">
							<Typography component="li">
								<p className="flex">
									<span className="font-bold mr-2">1.</span>Collection
								</p>
								<ul className="pl-6 mt-2">
									<Typography component="li">
										<div className="flex mb-2">
											<span className="font-bold mr-2">1.1</span> Information MBS may collect
										</div>
										
										<p className="pl-6">For the purposes of this privacy statement, ‘Personal Information’ is any data which relates to an individual who may be identified from that data, or from a combination of a set of data, and other information which is in possession of MBS. In general, you may browse our website without providing any Personal Information about yourself. However, we collect certain information such as.</p>
										
										<ul className="pl-6 mt-2">
											<Typography component="li" className="flex">
												<span className="font-medium mr-2">a.</span>Personal Information: You may be required to provide certain information such as Your name, (current & former), Date of birth, address (address history if applicable), email ID, phone number, Next of Kin details, photograph, documents to prove identity or address, Gender & Ethnicity , Health information (please mention if any physical disability also) including drug test results, Driving records, place of birth, resume, cover letter, notice period required to serve in your current organization, desired salary, training, professional qualifications and skills, education history, work experience, prior employment obligations and job related preferences and constraints, BG check related details etc., and any other public record information and any other details capable of identifying You (“Information”) for the provision of the Services to You via the Platform. MBS collects only such Personal Information that MBS believes to be relevant and is required to understand You or Your interests and for the provision of the Services.
											</Typography>
											<Typography component="li" className="flex">
												<span className="font-medium mr-2">b.</span>Non-Personal Information: MBS may collect technical data such as the IP address of Your device, device ID, the IMEI of Your mobile device, IMSI of Your SIM, etc. As this will not contain any personally identifiable information, MBS may use this Non-Personal Information in any manner as MBS deems fit.
											</Typography>
											<Typography component="li">
												<p className="flex">
													<span className="font-medium mr-2">c.</span>Cookies and Usage Data:
												</p>
												<ul className="pl-6 mt-2">
													<Typography component="li" className="flex">
														<span className="font-medium mr-2">i.</span>Usage Data We may also collect information how the Service is accessed and used (“Usage Data”). This Usage Data may include information such as your computer’s Internet Protocol address (e.g. IP address), browser type, browser version, the pages of our Service that you visit, the time and date of your visit, the time spent on those pages, unique device identifiers and other diagnostic data
													</Typography>
													<Typography component="li" className="flex">
														<span className="font-medium mr-2">ii.</span>Tracking & Cookies Data We use cookies and similar tracking technologies to track the activity on our Service and hold certain information.
													</Typography>
													<Typography component="li" className="flex">
														<span className="font-medium mr-2">iii.</span>Cookies are files with small amount of data which may include an anonymous unique identifier. Cookies are sent to your browser from a website and stored on your device. Tracking technologies also used are beacons, tags, and scripts to collect and track information and to improve and analyse our Service.
													</Typography>
													<Typography component="li" className="flex">
														<span className="font-medium mr-2">iv.</span>You can instruct your browser to refuse all cookies or to indicate when a cookie is being sent. However, if you do not accept cookies, you may not be able to use some portions of our Service
													</Typography>
													<Typography component="li" className="">
														<p className="flex">
															<span className="font-medium mr-2">v.</span>Examples of Cookies we use:
														</p>
														<ul className="pl-10 list-disc mt-2">
															<Typography component="li">
																<span className="font-bold">Session Cookies.</span>We use Session Cookies to operate our Service.
															</Typography>
															<Typography component="li">
																<span className="font-bold">Preference Cookies.</span>We use Preference Cookies to remember your preferences and various settings.
															</Typography>
															<Typography component="li">
																<span className="font-bold">Security Cookies.</span>We use Security Cookies for security purposes.
															</Typography>
														</ul>
													</Typography>
												</ul>
											</Typography>
										</ul>
									</Typography>
								</ul>
							</Typography>

							<Typography component="li">
								<p className="flex mb-2">
									<span className="font-bold mr-2">2.</span>Use of Information By MBS
								</p>
								<p className="pl-6">The Information provided by You to MBS will be kept confidential to the maximum possible extent, and may be used for a number of purposes connected with MBS‟s business operations which may include the following:</p>
								<p className="pl-6 mt-2">To provide and maintain the Service</p>
								<ul className="pl-6 mt-2">
									<Typography component="li">
										<span className="font-medium mr-2">a.</span>To notify you about changes to our Service
									</Typography>
									<Typography component="li">
										<span className="font-medium mr-2">b.</span>To provide better usability, troubleshooting and site maintenance
									</Typography>
									<Typography component="li">
										<span className="font-medium mr-2">c.</span>To allow you to participate in interactive features of our Service when you choose to do so
									</Typography>
									<Typography component="li">
										<span className="font-medium mr-2">d.</span>To provide customer care and support
									</Typography>
									<Typography component="li">
										<span className="font-medium mr-2">e.</span>To provide analysis or valuable information so that we can improve the Service
									</Typography>
									<Typography component="li">
										<span className="font-medium mr-2">f.</span>To monitor the usage of the Service
									</Typography>
									<Typography component="li">
										<span className="font-medium mr-2">g.</span>To detect, prevent and address technical issues
									</Typography>
									<Typography component="li">
										<span className="font-medium mr-2">h.</span>dealing with requests, enquiries and complaints and customer related activities;
									</Typography>
									<Typography component="li">
										<span className="font-medium mr-2">i.</span>to provide access to desirable content based on your preferences.
									</Typography>
									<Typography component="li">
										<span className="font-medium mr-2">j.</span>marketing products and services and data analysis, including for providing advertisements and surveys of relevance to You;
									</Typography>
									<Typography component="li">
										<span className="font-medium mr-2">k.</span>personalizing and enhancing Your experience while using the Platform by presenting products and offers tailored to You; and
									</Typography>
									<Typography component="li">
										<span className="font-medium mr-2">l.</span>abiding with laws and law enforcement / regulatory requests
									</Typography>
								</ul>
							</Typography>

							<Typography component="li">
								<p className="flex mb-2">
									<span className="font-bold mr-2">3.</span>Disclosure
								</p>
								<ul className="pl-6 mt-2">
									<Typography component="li">
										<p className="flex mb-2">
											<span className="font-medium  mr-2">3.1</span>by accessing this website, you hereby give your express consent to share your Personal Information within:
										</p>
										<ul className="pl-10 list-disc mt-2">
											<Typography component="li">MBS</Typography>
											<Typography component="li">Business partners;</Typography>
											<Typography component="li">Service vendors;</Typography>
											<Typography component="li">Authorized third-party agents; or</Typography>
										</ul>
									</Typography>
									<Typography component="li">
										<div className="flex mb-2">
											<span className="font-medium  mr-2">3.2</span>
											<p className="break-all">Additionally, Your consent may be implicit or implied or through course of conduct as necessary or appropriate or where the Information may be disclosed as follows: (a) in any manner permitted under applicable law, including laws outside Your country of residence; (b) to comply with legal process whether local or foreign; (c) to respond to requests from public and government authorities, including public and government authorities outside Your country of residence;(d)to enforcethetermsandconditionsofthePlatform;(e)toprotectMBS‟srights,privacy,safetyor property,and/orthatofMBS‟saffiliates,Youorothers;and(f)toallowMBStopursueavailable remedies or limit the damages that MBS may sustain.</p>
										</div>
									</Typography>
								</ul>
							</Typography>

							<Typography component="li">
								<p className="flex mb-2">
									<span className="font-bold mr-2">4.</span>Google Analytics
								</p>
								<ul className="pl-6 mt-2">
									<Typography component="li">
										<p className="flex mb-2">
											<span className="font-medium  mr-2">4.1</span>
											Google Analytics is a web analytics service offered by Google that tracks and reports website traffic. Google uses the data collected to track and monitor the use of our Service. This data is shared with other Google services. Google may use the collected data to contextualize and personalize the ads of its own advertising network. <br></br> you can opt-out of having made your activity on the Service available to Google Analytics by installing the Google Analytics opt-out browser add-on. The add-on prevents the Google Analytics JavaScript (ga.js, analytics.js, and dc.js) from sharing information with Google Analytics about visits activity.
										</p>
									</Typography>
									<Typography component="li">
										<div className="flex mb-2">
											<span className="font-medium  mr-2">4.2</span>
											<p>For more information on the privacy practices of Google, please visit the Google Privacy & Terms web page: &nbsp; <a href="https://policies.google.com/privacy?hl=en" className="text-secondary" target="_blank">https://policies.google.com/privacy?hl=en</a></p>
										</div>
									</Typography>
								</ul>
							</Typography>

							<Typography component="li">
								<p className="flex mb-2">
									<span className="font-bold mr-2">5.</span>Links To Other Sites
								</p>
								<ul className="pl-6 mt-2">
									<Typography component="li">
										<p className="flex mb-2">
											<span className="font-medium mr-2">5.1</span>
											Our Service may contain links to other sites that are not operated by us. If you click on a third party link, you will be directed to that third party’s site. We strongly advise you to review the Privacy Policy of every site you visit.
										</p>
									</Typography>
									<Typography component="li">
										<p className="flex mb-2">
											<span className="font-medium mr-2">5.2</span>
											We have no control over and assume no responsibility for the content, privacy policies or practices of any third-party sites or services.	
										</p>
									</Typography>
								</ul>
							</Typography>

							<Typography component="li">
								<p className="flex mb-2">
									<span className="font-bold mr-2">6.</span>Children’s Privacy
								</p>
								<ul className="pl-6 mt-2">
									<Typography component="li">
										<p className="flex mb-2">
											<span className="font-medium mr-2">6.1</span>
											Our Service does not address anyone under the age of 18(“Children”).
										</p>
									</Typography>
									<Typography component="li">
										<p className="flex mb-2">
											<span className="font-medium mr-2">6.2</span>
											We do not knowingly collect personally identifiable information from anyone under the age of 18. If you are a parent or guardian and you are aware that your Children has provided us with Personal Data, please contact us. If we become aware that we have collected Personal Data from children without verification of parental consent, we take steps to remove that information from our servers.
										</p>
									</Typography>
								</ul>
							</Typography>

							<Typography component="li">
								<p className="flex mb-2">
									<span className="font-bold mr-2">7.</span>Information Security And Storage
								</p>
								<ul className="pl-6 mt-2">
									<Typography component="li">
										<p className="flex mb-2">
											<span className="font-medium mr-2">7.1</span>
											MBS uses reasonable security measures, at the minimum those mandated under Information Technology Act, 2000 as amended and read with Information Technology (Reasonable Security Practices and Procedures and Sensitive Personal Data or Information) Rules, 2011, to safeguard and protect Your data and information. MBS has adopted reasonable security practices and procedures, in line with IS/ISO/IEC 27001, to include, strategic, operational, managerial, technical, and physical security controls to safeguard and protect Your data and information. MBS has implemented such measures, as stated above, to protect against unauthorized access to, and unlawful interception of Your Information. However, security risk cannot be completely eliminated while using the internet. You accept the inherent security implications of providing information over the internet and agree not to hold MBS responsible for any breach of security or the disclosure of personal information unless MBS has been grossly and willfully negligent.
										</p>
									</Typography>
									<Typography component="li">
										<p className="flex mb-2">
											<span className="font-medium mr-2">7.2</span>
											Notwithstanding anything contained in this Policy or elsewhere, MBS shall be under no liability whatsoever in case of occurrence of a force majeure event, including in case of non-availability of any portion of the Platform and/or the Services occasioned by any act of God, war, disease, revolution, riot, civil commotion, strike, lockout, flood, fire, failure of any public utility, man- made disaster, infrastructure failure, technology outages, failure of technology integration of partners or any other cause whatsoever, beyond the control of MBS. Further, in case of a force majeure event, MBS shall not be liable for any breach of security or loss of data / Information or any Content uploaded by You to the Platform.
										</p>
									</Typography>
									<Typography component="li">
										<p className="flex mb-2">
											<span className="font-medium mr-2">7.3</span>
											Our Retention of Personal Data: MBS shall retains personal data for as long as necessary to provide the access to and use of the website, or for other essential purposes such as complying with our legal obligations, resolving disputes and enforcing our agreements. Because these needs can vary for different data types and purposes, actual retention periods can vary significantly.
										</p>
									</Typography>
									<Typography component="li">
										<p className="flex mb-2">
											<span className="font-medium mr-2">7.4</span>
											Data Deletion or Shredding: Any documents that contain sensitive or confidential information (and particularly sensitive personal data) will be disposed of as confidential waste and be subject to secure electronic deletion; some expired or superseded contracts may only warrant in-house shredding. The Document Disposal / Shredding will be carried out as per the Scheduled frequency.
										</p>
									</Typography>
								</ul>
							</Typography>

							<Typography component="li">
								<p className="flex mb-2">
									<span className="font-bold mr-2">8.</span>Governing Law And Jurisdiction
								</p>
								<p className="pl-6 mt-2">Any dispute over privacy issues will be subject to this Policy, the Terms of Service, and the law of the Republic of India. Courts in Bangalore, Karnataka alone shall have exclusive jurisdiction to hear disputes arising out of this Policy.</p>
								
								<p className="pl-6 mt-2">If You have questions or concerns about this Policy, please contact at the below mentioned details:</p>

								<p className="pl-6 mt-2">Manipal Technologies Limited (H.O.)</p>
								<p className="pl-6 mt-2">Udayavani Building, Udayavani Road, Manipal-576 104.</p>
								<p className="pl-6 mt-2">Contact No.+91 820 2205000, +91 820 4275000</p>
								<p className="pl-6 mt-2">E-mail: <a href="mailto:<EMAIL>" target="_blank" className="text-secondary"><EMAIL></a></p>
							</Typography>

						</ul>
					</div>
				</div>
			</section>
			<SectionLower last />
		</div>
	);
};

export default Page;

export const Head: HeadFC<pageQueryResult> = ({ data }) => <SEO seo={data.allSanityPage.nodes[0].seo} />;

export const query = graphql`
	query PageQuery {
		allSanityPage(filter: { slug: { current: { eq: "book-demo" } } }) {
			nodes {
				title
				seo {
					title
					description
					keywords
					canonicalUrl
					ogTitle
					ogDescription
					ogImage {
						asset {
							publicUrl
							url
						}
					}
					noindex
					structuredData
				}
				hero {
					title
					subtitle
					cta
					ctaIcon {
						svg
					}
					ctaLink
					bgImage {
						asset {
							gatsbyImageData
							url
							publicUrl
						}
						alt
						title
					}
					imageAlign
				}
				sections {
					title
					subtitle
					id
					bg
					statistics {
						titleString
						titleNumber
						subtitle
						type
						icon {
							svg
						}
						image {
							asset {
								gatsbyImageData
								url
								publicUrl
							}
							alt
							title
						}
					}
					content {
						type
						content {
							title
							content: _rawContent
							cta
							ctaIcon {
								svg
							}
							ctaLink
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							imageAlign
							imageAsBg
						}
						timeline {
							title
							date
							description: _rawDescription
						}
						bigList {
							title
							description: _rawDescription
						}
						carousel {
							type
							delay
							contentType
							iconCards {
								type
								image {
									asset {
										gatsbyImageData
										url
										publicUrl
									}
									alt
									title
								}
								icon {
									svg
								}
								title
								description: _rawDescription
							}
							carousel {
								title
								content: _rawContent
								cta
								ctaIcon {
									svg
								}
								ctaLink
								image {
									asset {
										gatsbyImageData
										url
										publicUrl
									}
									alt
									title
								}
								imageAlign
								imageAsBg
							}
						}
						iconCards {
							title
							type
							icon {
								svg
							}
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							description: _rawDescription
						}
						icons {
							title
							type
							icon {
								svg
							}
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
						}
						offices {
							name
							description
							address
							phones
							fax
							email
							location {
								lat
								lng
							}
						}
						employees {
							name
							position
							profile: _rawProfile
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
						}
						product {
							title
							cta
							ctaLink
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							highlights: _rawHighlights
							description: _rawDescription
						}
						tabs {
							title
							content {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								timeline {
									title
									date
									description: _rawDescription
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								columns {
									splitRatio
									sticky
									contentLeft {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
									contentRight {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
						}
						columns {
							splitRatio
							sticky
							contentLeft {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								timeline {
									title
									date
									description: _rawDescription
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								tabs {
									title
									content {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
							contentRight {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								timeline {
									title
									date
									description: _rawDescription
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								tabs {
									title
									content {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
`;
